<!doctype html>
<html lang="en-US">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    <title>Contact - Next Level Growth Partners | Get Your AI Demo</title>
    <meta name='robots' content='max-image-preview:large' />
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px
        }
    </style>
    <link rel="alternate" type="application/rss+xml" title="Brandmode &raquo; Feed"
        href="https://brandmode.1onestrong.com/feed/" />
    <link rel="alternate" type="application/rss+xml" title="Brandmode &raquo; Comments Feed"
        href="https://brandmode.1onestrong.com/comments/feed/" />
    <script>
        window._wpemojiSettings = { "baseUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/72x72\/", "ext": ".png", "svgUrl": "https:\/\/s.w.org\/images\/core\/emoji\/15.1.0\/svg\/", "svgExt": ".svg", "source": { "concatemoji": "https:\/\/brandmode.1onestrong.com\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.1" } };
        /*! This file is auto-generated */
        !function (i, n) { var o, s, e; function c(e) { try { var t = { supportTests: e, timestamp: (new Date).valueOf() }; sessionStorage.setItem(o, JSON.stringify(t)) } catch (e) { } } function p(e, t, n) { e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(t, 0, 0); var t = new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data), r = (e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.fillText(n, 0, 0), new Uint32Array(e.getImageData(0, 0, e.canvas.width, e.canvas.height).data)); return t.every(function (e, t) { return e === r[t] }) } function u(e, t, n) { switch (t) { case "flag": return n(e, "\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f", "\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f") ? !1 : !n(e, "\ud83c\uddfa\ud83c\uddf3", "\ud83c\uddfa\u200b\ud83c\uddf3") && !n(e, "\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f", "\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f"); case "emoji": return !n(e, "\ud83d\udc26\u200d\ud83d\udd25", "\ud83d\udc26\u200b\ud83d\udd25") }return !1 } function f(e, t, n) { var r = "undefined" != typeof WorkerGlobalScope && self instanceof WorkerGlobalScope ? new OffscreenCanvas(300, 150) : i.createElement("canvas"), a = r.getContext("2d", { willReadFrequently: !0 }), o = (a.textBaseline = "top", a.font = "600 32px Arial", {}); return e.forEach(function (e) { o[e] = t(a, e, n) }), o } function t(e) { var t = i.createElement("script"); t.src = e, t.defer = !0, i.head.appendChild(t) } "undefined" != typeof Promise && (o = "wpEmojiSettingsSupports", s = ["flag", "emoji"], n.supports = { everything: !0, everythingExceptFlag: !0 }, e = new Promise(function (e) { i.addEventListener("DOMContentLoaded", e, { once: !0 }) }), new Promise(function (t) { var n = function () { try { var e = JSON.parse(sessionStorage.getItem(o)); if ("object" == typeof e && "number" == typeof e.timestamp && (new Date).valueOf() < e.timestamp + 604800 && "object" == typeof e.supportTests) return e.supportTests } catch (e) { } return null }(); if (!n) { if ("undefined" != typeof Worker && "undefined" != typeof OffscreenCanvas && "undefined" != typeof URL && URL.createObjectURL && "undefined" != typeof Blob) try { var e = "postMessage(" + f.toString() + "(" + [JSON.stringify(s), u.toString(), p.toString()].join(",") + "));", r = new Blob([e], { type: "text/javascript" }), a = new Worker(URL.createObjectURL(r), { name: "wpTestEmojiSupports" }); return void (a.onmessage = function (e) { c(n = e.data), a.terminate(), t(n) }) } catch (e) { } c(n = f(s, u, p)) } t(n) }).then(function (e) { for (var t in e) n.supports[t] = e[t], n.supports.everything = n.supports.everything && n.supports[t], "flag" !== t && (n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && n.supports[t]); n.supports.everythingExceptFlag = n.supports.everythingExceptFlag && !n.supports.flag, n.DOMReady = !1, n.readyCallback = function () { n.DOMReady = !0 } }).then(function () { return e }).then(function () { var e; n.supports.everything || (n.readyCallback(), (e = n.source || {}).concatemoji ? t(e.concatemoji) : e.wpemoji && e.twemoji && (t(e.twemoji), t(e.wpemoji))) })) }((window, document), window._wpemojiSettings);
    </script>
    <!-- Webhook Configuration -->
    <script src="./webhook-config.js"></script>
    <!-- n8n Chat Integration Widget -->
    <script src="n8n-chat-integration.js"></script>
    <style id='wp-emoji-styles-inline-css'>
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
    <style id='global-styles-inline-css'>
        :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--font-size--small: 13px;
            --wp--preset--font-size--medium: 20px;
            --wp--preset--font-size--large: 36px;
            --wp--preset--font-size--x-large: 42px;
            --wp--preset--spacing--20: 0.44rem;
            --wp--preset--spacing--30: 0.67rem;
            --wp--preset--spacing--40: 1rem;
            --wp--preset--spacing--50: 1.5rem;
            --wp--preset--spacing--60: 2.25rem;
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }

        :root {
            --wp--style--global--content-size: 800px;
            --wp--style--global--wide-size: 1200px;
        }

        :where(body) {
            margin: 0;
        }

        .wp-site-blocks>.alignleft {
            float: left;
            margin-right: 2em;
        }

        .wp-site-blocks>.alignright {
            float: right;
            margin-left: 2em;
        }

        .wp-site-blocks>.aligncenter {
            justify-content: center;
            margin-left: auto;
            margin-right: auto;
        }

        :where(.wp-site-blocks)>* {
            margin-block-start: 24px;
            margin-block-end: 0;
        }

        :where(.wp-site-blocks)> :first-child {
            margin-block-start: 0;
        }

        :where(.wp-site-blocks)> :last-child {
            margin-block-end: 0;
        }

        :root {
            --wp--style--block-gap: 24px;
        }

        :root :where(.is-layout-flow)> :first-child {
            margin-block-start: 0;
        }

        :root :where(.is-layout-flow)> :last-child {
            margin-block-end: 0;
        }

        :root :where(.is-layout-flow)>* {
            margin-block-start: 24px;
            margin-block-end: 0;
        }

        :root :where(.is-layout-constrained)> :first-child {
            margin-block-start: 0;
        }

        :root :where(.is-layout-constrained)> :last-child {
            margin-block-end: 0;
        }

        :root :where(.is-layout-constrained)>* {
            margin-block-start: 24px;
            margin-block-end: 0;
        }

        :root :where(.is-layout-flex) {
            gap: 24px;
        }

        :root :where(.is-layout-grid) {
            gap: 24px;
        }

        .is-layout-flow>.alignleft {
            float: left;
            margin-inline-start: 0;
            margin-inline-end: 2em;
        }

        .is-layout-flow>.alignright {
            float: right;
            margin-inline-start: 2em;
            margin-inline-end: 0;
        }

        .is-layout-flow>.aligncenter {
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-layout-constrained>.alignleft {
            float: left;
            margin-inline-start: 0;
            margin-inline-end: 2em;
        }

        .is-layout-constrained>.alignright {
            float: right;
            margin-inline-start: 2em;
            margin-inline-end: 0;
        }

        .is-layout-constrained>.aligncenter {
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-layout-constrained> :where(:not(.alignleft):not(.alignright):not(.alignfull)) {
            max-width: var(--wp--style--global--content-size);
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-layout-constrained>.alignwide {
            max-width: var(--wp--style--global--wide-size);
        }

        body .is-layout-flex {
            display: flex;
        }

        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }

        .is-layout-flex> :is(*, div) {
            margin: 0;
        }

        body .is-layout-grid {
            display: grid;
        }

        .is-layout-grid> :is(*, div) {
            margin: 0;
        }

        body {
            padding-top: 0px;
            padding-right: 0px;
            padding-bottom: 0px;
            padding-left: 0px;
        }

        a:where(:not(.wp-element-button)) {
            text-decoration: underline;
        }

        :root :where(.wp-element-button, .wp-block-button__link) {
            background-color: #32373c;
            border-width: 0;
            color: #fff;
            font-family: inherit;
            font-size: inherit;
            line-height: inherit;
            padding: calc(0.667em + 2px) calc(1.333em + 2px);
            text-decoration: none;
        }

        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }

        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }

        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }

        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }

        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }

        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }

        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }

        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }

        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }

        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }

        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }

        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }

        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }

        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }

        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }

        :root :where(.wp-block-pullquote) {
            font-size: 1.5em;
            line-height: 1.6;
        }
        .elementor-message-danger{
            display: none !important;
        }

        .elementor-message-danger::before{
            display: none;
        }

             .site-logo-img {
            max-height: 50px;
            width: auto;
            display: block;
            margin: 0;
            padding: 0;
            border: none;
            background: none;
        }
    </style>
    <link rel='stylesheet' id='template-kit-export-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/template-kit-export/assets/public/template-kit-export-public.css?ver=1.0.23'
        media='all' />
    <link rel='stylesheet' id='hello-elementor-css'
        href='https://brandmode.1onestrong.com/wp-content/themes/hello-elementor/style.min.css?ver=3.3.0' media='all' />
    <link rel='stylesheet' id='hello-elementor-theme-style-css'
        href='https://brandmode.1onestrong.com/wp-content/themes/hello-elementor/theme.min.css?ver=3.3.0' media='all' />
    <link rel='stylesheet' id='hello-elementor-header-footer-css'
        href='https://brandmode.1onestrong.com/wp-content/themes/hello-elementor/header-footer.min.css?ver=3.3.0'
        media='all' />
    <link rel='stylesheet' id='elementor-frontend-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/custom-frontend.min.css?ver=1738903761'
        media='all' />
    <link rel='stylesheet' id='elementor-post-10-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/post-10.css?ver=1738903761'
        media='all' />
    <link rel='stylesheet' id='widget-image-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/widget-image.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='widget-nav-menu-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/custom-pro-widget-nav-menu.min.css?ver=1738903761'
        media='all' />
    <link rel='stylesheet' id='widget-heading-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='widget-icon-list-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/custom-widget-icon-list.min.css?ver=1738903761'
        media='all' />
    <link rel='stylesheet' id='widget-form-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/css/widget-form.min.css?ver=3.27.2'
        media='all' />
    <link rel='stylesheet' id='e-animation-slideInUp-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/lib/animations/styles/slideInUp.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='widget-text-editor-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/widget-text-editor.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='widget-social-icons-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/widget-social-icons.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='e-apple-webkit-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/custom-apple-webkit.min.css?ver=1738903761'
        media='all' />
    <link rel='stylesheet' id='widget-spacer-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/css/widget-spacer.min.css?ver=3.27.3'
        media='all' />
    <link rel='stylesheet' id='elementor-post-2672-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/post-2672.css?ver=1738906710'
        media='all' />
    <link rel='stylesheet' id='elementor-post-3790-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/post-3790.css?ver=1738903762'
        media='all' />
    <link rel='stylesheet' id='elementor-post-1764-css'
        href='https://brandmode.1onestrong.com/wp-content/uploads/elementor/css/post-1764.css?ver=1738903762'
        media='all' />
    <link rel='stylesheet' id='ekit-widget-styles-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/css/widget-styles.css?ver=3.4.0'
        media='all' />
    <link rel='stylesheet' id='ekit-responsive-css'
        href='https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/css/responsive.css?ver=3.4.0'
        media='all' />
    <link rel='stylesheet' id='google-fonts-1-css'
        href='https://fonts.googleapis.com/css?family=DM+Sans%3A100%2C100italic%2C200%2C200italic%2C300%2C300italic%2C400%2C400italic%2C500%2C500italic%2C600%2C600italic%2C700%2C700italic%2C800%2C800italic%2C900%2C900italic%7CManrope%3A100%2C100italic%2C200%2C200italic%2C300%2C300italic%2C400%2C400italic%2C500%2C500italic%2C600%2C600italic%2C700%2C700italic%2C800%2C800italic%2C900%2C900italic%7CInter+Tight%3A100%2C100italic%2C200%2C200italic%2C300%2C300italic%2C400%2C400italic%2C500%2C500italic%2C600%2C600italic%2C700%2C700italic%2C800%2C800italic%2C900%2C900italic%7CNunito+Sans%3A100%2C100italic%2C200%2C200italic%2C300%2C300italic%2C400%2C400italic%2C500%2C500italic%2C600%2C600italic%2C700%2C700italic%2C800%2C800italic%2C900%2C900italic&#038;display=swap&#038;ver=6.8.1'
        media='all' />
    <link rel='stylesheet' id='elementor-icons-ekiticons-css'
        href='./css/ekiticons.css'
        media='all' />
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <script src="https://brandmode.1onestrong.com/wp-includes/js/jquery/jquery.min.js?ver=3.7.1"
        id="jquery-core-js"></script>
    <script src="https://brandmode.1onestrong.com/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1"
        id="jquery-migrate-js"></script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/template-kit-export/assets/public/template-kit-export-public.js?ver=1.0.23"
        id="template-kit-export-js"></script>
    <link rel="https://api.w.org/" href="https://brandmode.1onestrong.com/wp-json/" />
    <link rel="alternate" title="JSON" type="application/json"
        href="https://brandmode.1onestrong.com/wp-json/wp/v2/pages/2672" />
    <link rel="EditURI" type="application/rsd+xml" title="RSD" href="https://brandmode.1onestrong.com/xmlrpc.php?rsd" />
    <meta name="generator" content="WordPress 6.8.1" />
    <link rel="canonical" href="https://brandmode.1onestrong.com/contact/" />
    <link rel='shortlink' href='https://brandmode.1onestrong.com/?p=2672' />
    <link rel="alternate" title="oEmbed (JSON)" type="application/json+oembed"
        href="https://brandmode.1onestrong.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fbrandmode.1onestrong.com%2Fcontact%2F" />
    <link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed"
        href="https://brandmode.1onestrong.com/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fbrandmode.1onestrong.com%2Fcontact%2F&#038;format=xml" />
    <meta name="generator"
        content="Elementor 3.27.3; features: e_font_icon_svg, additional_custom_breakpoints; settings: css_print_method-external, google_font-enabled, font_display-swap">
    <style>
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
            background-image: none !important;
        }

        @media screen and (max-height: 1024px) {

            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }

        @media screen and (max-height: 640px) {

            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }
    </style>
    <link rel="icon" href="./img/image-logo.png" sizes="32x32" />
    <link rel="icon" href="./img/image-logo.png" sizes="192x192" />
    <link rel="apple-touch-icon" href="./img/image-logo.png" />
    <meta name="msapplication-TileImage"
        content="./img/image-logo.png" />
</head>

<body
    class="wp-singular page-template page-template-elementor_header_footer page page-id-2672 wp-custom-logo wp-embed-responsive wp-theme-hello-elementor theme-default elementor-default elementor-template-full-width elementor-kit-10 elementor-page elementor-page-2672">


    <a class="skip-link screen-reader-text" href="#content">Skip to content</a>

     <div data-elementor-type="header" data-elementor-id="3790"
        class="elementor elementor-3790 elementor-location-header" data-elementor-post-type="elementor_library">
        <div class="elementor-element elementor-element-27e5538 e-flex e-con-boxed e-con e-parent" data-id="27e5538"
            data-element_type="container">
            <div class="e-con-inner">
                <div class="elementor-element elementor-element-c931a08 e-con-full e-flex e-con e-child"
                    data-id="c931a08" data-element_type="container">
                    <div class="elementor-element elementor-element-f0c84a9 elementor-widget elementor-widget-theme-site-logo elementor-widget-image"
                        data-id="f0c84a9" data-element_type="widget" data-widget_type="theme-site-logo.default">
                        <div class="elementor-widget-container">
                            <a href="index.html" class="site-logo-link">
                                <img src="img/image.png" alt="Next Level Growth Partners" class="site-logo-img">
                            </a>
                        </div>
                    </div>
                </div>
                <div class="elementor-element elementor-element-573a350 e-con-full e-flex e-con e-child"
                    data-id="573a350" data-element_type="container">
                    <div class="elementor-element elementor-element-e861c87 elementor-nav-menu__align-center elementor-nav-menu--stretch elementor-nav-menu--dropdown-tablet elementor-nav-menu__text-align-aside elementor-nav-menu--toggle elementor-nav-menu--burger elementor-widget elementor-widget-nav-menu"
                        data-id="e861c87" data-element_type="widget"
                        data-settings="{&quot;submenu_icon&quot;:{&quot;value&quot;:&quot;&lt;i class=\&quot;\&quot;&gt;&lt;\/i&gt;&quot;,&quot;library&quot;:&quot;&quot;},&quot;full_width&quot;:&quot;stretch&quot;,&quot;layout&quot;:&quot;horizontal&quot;,&quot;toggle&quot;:&quot;burger&quot;}"
                        data-widget_type="nav-menu.default">
                        <div class="elementor-widget-container">
                            <nav aria-label="Menu"
                                class="elementor-nav-menu--main elementor-nav-menu__container elementor-nav-menu--layout-horizontal e--pointer-background e--animation-fade">
                                <ul id="menu-1-e861c87" class="elementor-nav-menu">
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom current-menu-ancestor current-menu-parent menu-item-has-children menu-item-3808">
                                        <a class="elementor-item" href="index.html">Home</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3809">
                                        <a class="elementor-item" href="service.html">AI Solutions</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3812">
                                        <a class="elementor-item" href="resource.html">Resources</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3813">
                                        <a class="elementor-item" href="blogs-list.html">Blogs</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3810">
                                        <a class="elementor-item" href="about.html">About</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3811">
                                        <a class="elementor-item" href="faq.html">FAQs</a>
                                    </li>
                                    <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3798">
                                        <a href="contact.html"
                                            class="elementor-item">Contact</a>
                                    </li>
                                </ul>
                            </nav>
                            <div class="elementor-menu-toggle" role="button" tabindex="0" aria-label="Menu Toggle"
                                aria-expanded="false">
                                <i aria-hidden="true" role="presentation"
                                    class="elementor-menu-toggle__icon--open icon icon-burger-menu"></i><svg
                                    aria-hidden="true" role="presentation"
                                    class="elementor-menu-toggle__icon--close e-font-icon-svg e-eicon-close"
                                    viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M742 167L500 408 258 167C246 154 233 150 217 150 196 150 179 158 167 167 154 179 150 196 150 212 150 229 154 242 171 254L408 500 167 742C138 771 138 800 167 829 196 858 225 858 254 829L496 587 738 829C750 842 767 846 783 846 800 846 817 842 829 829 842 817 846 804 846 783 846 767 842 750 829 737L588 500 833 258C863 229 863 200 833 171 804 137 775 137 742 167Z">
                                    </path>
                                </svg>
                            </div>
                            <nav class="elementor-nav-menu--dropdown elementor-nav-menu__container" aria-hidden="true">
                                <ul id="menu-2-e861c87" class="elementor-nav-menu">
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom current-menu-ancestor current-menu-parent menu-item-has-children menu-item-3808">
                                        <a class="elementor-item" tabindex="-1" href="index.html">Home</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3809">
                                        <a class="elementor-item" tabindex="-1" href="service.html">AI Solutions</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3812">
                                        <a class="elementor-item" tabindex="-1" href="resource.html">Resources</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3813">
                                        <a class="elementor-item" tabindex="-1" href="blogs-list.html">Blogs</a>

                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3810">
                                        <a class="elementor-item" tabindex="-1" href="about.html">About</a>
                                    </li>
                                    <li
                                        class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-4375">
                                        <a class="elementor-item" tabindex="-1" href="faq.html"> FAQs</a>
                                    </li>
                                    <li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3798">
                                        <a href="contact.html" class="elementor-item"
                                            tabindex="-1">Contact</a>
                                    </li>
                            </nav>
                        </div>
                    </div>
                </div>
                <div class="elementor-element elementor-element-bea445e elementor-hidden-tablet elementor-hidden-mobile e-con-full e-flex e-con e-child"
                    data-id="bea445e" data-element_type="container">
                    <div class="elementor-element elementor-element-ebe72f0 elementor-align-right elementor-widget elementor-widget-elementskit-button"
                        data-id="ebe72f0" data-element_type="widget" data-widget_type="elementskit-button.default">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div data-elementor-type="wp-page" data-elementor-id="2672" class="elementor elementor-2672"
        data-elementor-post-type="page">
        <div class="elementor-element elementor-element-fe527ec e-con-full e-flex e-con e-parent" data-id="fe527ec"
            data-element_type="container"
            data-settings="{&quot;background_background&quot;:&quot;gradient&quot;,&quot;background_motion_fx_motion_fx_scrolling&quot;:&quot;yes&quot;,&quot;background_motion_fx_translateX_effect&quot;:&quot;yes&quot;,&quot;background_motion_fx_translateX_speed&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:6.5,&quot;sizes&quot;:[]},&quot;background_motion_fx_translateX_affectedRange&quot;:{&quot;unit&quot;:&quot;%&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:{&quot;start&quot;:0,&quot;end&quot;:100}},&quot;background_motion_fx_devices&quot;:[&quot;desktop&quot;,&quot;laptop&quot;,&quot;tablet&quot;,&quot;mobile&quot;]}">
            <div class="elementor-element elementor-element-06d5aaa e-flex e-con-boxed elementor-invisible e-con e-child"
                data-id="06d5aaa" data-element_type="container"
                data-settings="{&quot;animation&quot;:&quot;slideInUp&quot;,&quot;animation_delay&quot;:150}">
                <div class="e-con-inner">
                    <div class="elementor-element elementor-element-29348bc e-con-full e-flex e-con e-child"
                        data-id="29348bc" data-element_type="container">
                       
                
                    </div>
                    <div class="elementor-element elementor-element-92a143c elementor-widget__width-initial elementor-widget elementor-widget-elementskit-heading"
                        data-id="92a143c" data-element_type="widget" data-widget_type="elementskit-heading.default">
                        <div class="elementor-widget-container">
                            <div class="ekit-wid-con">
                                <div
                                    class="ekit-heading elementskit-section-title-wraper text_center   ekit_heading_tablet-   ekit_heading_mobile-">
                                    <h2 class="ekit-heading--title elementskit-section-title text_fill">Ready to Get the perks of a 24/7 Sales Team
                                        <span><span> WITHOUTHE the expense?</span></span></h2>
                                    <div class='ekit-heading__description'>
                                        <p>Book a demo to see how our AI can transform your lead conversion process.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="elementor-element elementor-element-b169fd3 e-con-full e-flex e-con e-parent" data-id="b169fd3"
            data-element_type="container">
            <div class="elementor-element elementor-element-477a820 e-con-full e-flex e-con e-child" data-id="477a820"
                data-element_type="container">
                <div class="elementor-element elementor-element-d94708f elementor-button-align-stretch elementor-widget elementor-widget-form"
                    data-id="d94708f" data-element_type="widget"
                    data-settings="{&quot;step_next_label&quot;:&quot;Next&quot;,&quot;step_previous_label&quot;:&quot;Previous&quot;,&quot;button_width&quot;:&quot;100&quot;,&quot;step_type&quot;:&quot;number_text&quot;,&quot;step_icon_shape&quot;:&quot;circle&quot;}"
                    data-widget_type="form.default">
                    <div class="elementor-widget-container">
                        <form class="elementor-form" method="post" name="New Form">
                            <input type="hidden" name="post_id" value="2672" />
                            <input type="hidden" name="form_id" value="d94708f" />
                            <input type="hidden" name="referer_title" value="Contact" />

                            <input type="hidden" name="queried_id" value="2672" />

                            <div class="elementor-form-fields-wrapper elementor-labels-above">
                                <div
                                    class="elementor-field-type-text elementor-field-group elementor-column elementor-field-group-first-name elementor-col-50 elementor-field-required">
                                    <label for="form-field-first-name" class="elementor-field-label">
                                        First Name *</label>
                                    <input size="1" type="text" name="form_fields[first_name]" id="form-field-first-name"
                                        class="elementor-field elementor-size-md  elementor-field-textual"
                                        placeholder="First Name" required="required">
                                </div>
                                <div
                                    class="elementor-field-type-text elementor-field-group elementor-column elementor-field-group-last-name elementor-col-50 elementor-field-required">
                                    <label for="form-field-last-name" class="elementor-field-label">
                                        Last Name *</label>
                                    <input size="1" type="text" name="form_fields[last_name]" id="form-field-last-name"
                                        class="elementor-field elementor-size-md  elementor-field-textual"
                                        placeholder="Last Name" required="required">
                                </div>
                                <div
                                    class="elementor-field-type-tel elementor-field-group elementor-column elementor-field-group-phone elementor-col-50 elementor-field-required">
                                    <label for="form-field-phone" class="elementor-field-label">
                                        Phone *</label>
                                    <input size="1" type="tel" name="form_fields[phone]" id="form-field-phone"
                                        class="elementor-field elementor-size-md  elementor-field-textual"
                                        placeholder="Phone">
                                </div>
                                <div
                                    class="elementor-field-type-email elementor-field-group elementor-column elementor-field-group-email elementor-col-50 elementor-field-required">
                                    <label for="form-field-email" class="elementor-field-label">
                                        Email *</label>
                                    <input size="1" type="email" name="form_fields[email]" id="form-field-email"
                                        class="elementor-field elementor-size-md  elementor-field-textual"
                                        placeholder="Email" required="required">
                                </div>
<div
    class="elementor-field-type-text elementor-field-group elementor-column elementor-field-group-company elementor-col-100 elementor-field-required">
    <label for="form-field-company" class="elementor-field-label">
        Company Website *
    </label>
    <input 
        size="1" 
        type="text" 
        name="form_fields[company]" 
        id="form-field-company"
        class="elementor-field elementor-size-md elementor-field-textual"
        placeholder="Company Website" 
        required
    >
    <span id="website-error" style="color: red; font-size: 14px; display: none;">Please enter a valid website (e.g., www.example.com)</span>
</div>

<script>
document.addEventListener("DOMContentLoaded", function () {
    const form = document.querySelector("form.elementor-form");
    const websiteInput = document.getElementById("form-field-company");
    const phoneInput = document.getElementById("form-field-phone");
    const submitBtn = form.querySelector("button[type='submit']");
    const websiteError = document.getElementById("website-error");

    // function validateWebsite() {
    //     const pattern = /^www\.[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/;
    //     const isValid = pattern.test(websiteInput.value.trim());
    //     websiteError.style.display = isValid ? "none" : "block";
    //     websiteInput.style.border = isValid ? "" : "1px solid red";
    //     return isValid;
    // }
function validateWebsite() {
    const pattern = /^(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+\.[a-zA-Z0-9-]+\.[a-zA-Z]{2,}$/;
    const isValid = pattern.test(websiteInput.value.trim());
    websiteError.style.display = isValid ? "none" : "block";
    websiteInput.style.border = isValid ? "" : "1px solid red";
    return isValid;
}


    function validatePhone() {
        const pattern = /^\+?[0-9]{7,15}$/;
        const isValid = pattern.test(phoneInput.value.trim());
        phoneInput.style.border = isValid ? "" : "1px solid red";
        return isValid;
    }

    function checkFormValidity() {
        const requiredFields = form.querySelectorAll("[required]");
        let allValid = true;

        requiredFields.forEach(field => {
            if (field.type === "checkbox" && !field.checked) {
                allValid = false;
            } else if ((field.type === "text" || field.type === "email" || field.type === "tel") && field.value.trim() === "") {
                allValid = false;
            }
        });

        // Website validation ONLY when user has interacted with that input (use a flag)
        // But since you want website validation always for final submit, keep website validation always
        // You can skip website validation on form input except websiteInput events

        if (!validateWebsite() || !validatePhone()) {
            allValid = false;
        }

        submitBtn.disabled = !allValid;
        submitBtn.style.opacity = allValid ? "1" : "0.5";
        submitBtn.style.cursor = allValid ? "pointer" : "not-allowed";
    }

    // Bind website validation only on websiteInput events
    websiteInput.addEventListener("input", () => {
        validateWebsite();
        checkFormValidity();
    });

    websiteInput.addEventListener("blur", () => {
        validateWebsite();
        checkFormValidity();
    });

    // Phone input validation on phoneInput events
    phoneInput.addEventListener("input", () => {
        validatePhone();
        checkFormValidity();
    });

    // For rest of the form, just check validity but no website validation
    form.addEventListener("input", (e) => {
        if (e.target !== websiteInput) {  // skip website validation on generic input
            checkFormValidity();
        }
    });

    // Initially disable submit
    submitBtn.disabled = true;
    submitBtn.style.opacity = "0.5";
    submitBtn.style.cursor = "not-allowed";
});

</script>


                                <div
                                    class="elementor-field-type-select elementor-field-group elementor-column elementor-field-group-service elementor-col-50">
                                    <label for="form-field-service" class="elementor-field-label">
                                        Which product interests you the most? </label>
                                    <div class="elementor-field elementor-select-wrapper remove-before ">
                                        <div class="select-caret-down-wrapper">
                                            <svg aria-hidden="true" class="e-font-icon-svg e-eicon-caret-down"
                                                viewBox="0 0 571.4 571.4" xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M571 393Q571 407 561 418L311 668Q300 679 286 679T261 668L11 418Q0 407 0 393T11 368 36 357H536Q550 357 561 368T571 393Z">
                                                </path>
                                            </svg>
                                        </div>
                                        <select name="form_fields[service]" id="form-field-service"
                                            class="elementor-field-textual elementor-size-md">
                                           <option value="Lead Revival Android">Lead Revival Android</option>
            <option value="Instant Lead Android">Instant Lead Android</option>
            <option value="Abandoned Cart Recovery">Abandoned Cart Recovery</option>
            <option value="Out Of Hours Android">Out Of Hours Android</option>
            <option value="Voice AI Receptionist">Voice AI Receptionist</option>
            <option value="Convo AI Widget">Convo AI Widget</option>
            <option value="Custom Built Solution">Custom Built Solution</option>
            <option value="All of Them">All of Them</option>
                                        </select>
                                    </div>
                                </div>
                                <div
                                    class="elementor-field-type-select elementor-field-group elementor-column elementor-field-group-referral elementor-col-50">
                                    <label for="form-field-referral" class="elementor-field-label">
                                        How did you hear about us? </label>
                                    <div class="elementor-field elementor-select-wrapper remove-before ">
                                        <div class="select-caret-down-wrapper">
                                            <svg aria-hidden="true" class="e-font-icon-svg e-eicon-caret-down"
                                                viewBox="0 0 571.4 571.4" xmlns="http://www.w3.org/2000/svg">
                                                <path
                                                    d="M571 393Q571 407 561 418L311 668Q300 679 286 679T261 668L11 418Q0 407 0 393T11 368 36 357H536Q550 357 561 368T571 393Z">
                                                </path>
                                            </svg>
                                        </div>
                                        <select name="form_fields[referral]" id="form-field-referral"
                                            class="elementor-field-textual elementor-size-md">
                                            <option value="Google Search">Google Search</option>
                                            <option value="Social Media">Social Media</option>
                                            <option value="Referral">Referral</option>
                                            <option value="LinkedIn">LinkedIn</option>
                                            <option value="Facebook">Facebook</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                </div>
                                <!-- Your Role -->
<div class="elementor-field-type-text elementor-field-group elementor-column elementor-field-group-role elementor-col-50">
    <label for="form-field-role" class="elementor-field-label">
        Your role within the organization:
    </label>
    <input type="text" name="form_fields[role]" id="form-field-role"
           class="elementor-field elementor-size-md elementor-field-textual"
           placeholder="e.g., Marketing Manager, Founder, etc.">
</div>

<!-- Company Size -->
<div class="elementor-field-type-select elementor-field-group elementor-column elementor-field-group-company-size elementor-col-50">
    <label for="form-field-company-size" class="elementor-field-label">
        Company Size:
    </label>
    <div class="elementor-field elementor-select-wrapper remove-before">
        <div class="select-caret-down-wrapper">
            <svg aria-hidden="true" class="e-font-icon-svg e-eicon-caret-down"
                 viewBox="0 0 571.4 571.4" xmlns="http://www.w3.org/2000/svg">
                <path d="M571 393Q571 407 561 418L311 668Q300 679 286 679T261 668L11 418Q0 407 0 393T11 368 36 357H536Q550 357 561 368T571 393Z"></path>
            </svg>
        </div>
        <select name="form_fields[company_size]" id="form-field-company-size"
                class="elementor-field-textual elementor-size-md">
            <option value="1-10">1-10</option>
            <option value="11-50">11-50</option>
            <option value="51-200">51-200</option>
            <option value="201-500">201-500</option>
            <option value="500+">500+</option>
        </select>
    </div>
</div>

                                <div
                                    class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-message elementor-col-100">
                                    <label for="form-field-message" class="elementor-field-label">
                                        Tell us about your business and goals: </label>
                                    <textarea class="elementor-field-textual elementor-field  elementor-size-md"
                                        name="form_fields[message]" id="form-field-message" rows="4"
                                        placeholder="Describe your business, current challenges, and what you hope to achieve with AI automation..."></textarea>
                                </div>

                                <!-- SMS Consent Checkboxes -->
                                <div class="elementor-field-type-acceptance elementor-field-group elementor-column elementor-field-group-sms-consent elementor-col-100 elementor-field-required">
                                    <div class="elementor-field-subgroup">
                                        <span class="elementor-field-option">
                                            <input type="checkbox" value="yes" id="form-field-sms-consent" name="form_fields[sms_consent]" required="required">
                                            <label for="form-field-sms-consent">
                                              I consent to receive sms notifications, occasional marketing messages, & alerts for upcoming appointment details from Next Level Growth Partners. Message frequency may vary. Message & data rates may apply. Text HELP for assistance. Reply STOP to unsubscribe at any time. *

                                            </label>
                                        </span>
                                    </div>
                                </div>
<!-- 
                                <div class="elementor-field-type-acceptance elementor-field-group elementor-column elementor-field-group-terms elementor-col-100 elementor-field-required">
                                    <div class="elementor-field-subgroup">
                                        <span class="elementor-field-option">
                                            <input type="checkbox" value="yes" id="form-field-terms" name="form_fields[terms]" required="required">
                                            <label for="form-field-terms">
                                                I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>. *
                                            </label>
                                        </span>
                                    </div>
                                </div> -->

                                <div
                                    class="elementor-field-group elementor-column elementor-field-type-submit elementor-col-100 e-form__buttons">
                                    <button class="elementor-button elementor-size-lg" type="submit" style="background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 18px 40px; border-radius: 50px; font-weight: 600; font-size: 1.1rem; border: none; cursor: pointer; transition: all 0.3s ease;">
                                        <span class="elementor-button-content-wrapper">
                                            <span class="elementor-button-text">Request My Demo</span>
                                        </span>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-f544890 e-con-full e-flex e-con e-child" data-id="f544890"
                data-element_type="container">
                <div class="elementor-element elementor-element-d9e8a66 e-con-full e-flex e-con e-child"
                    data-id="d9e8a66" data-element_type="container">
                    <div class="elementor-element elementor-element-8a3f49d elementor-widget elementor-widget-heading"
                        data-id="8a3f49d" data-element_type="widget" data-widget_type="heading.default">
                        <div class="elementor-widget-container">
                            <h4 class="elementor-heading-title elementor-size-default"><u>Connect with us</u></h4>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-17c94e8 elementor-align-left elementor-icon-list--layout-traditional elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list"
                        data-id="17c94e8" data-element_type="widget" data-widget_type="icon-list.default">
                        <div class="elementor-widget-container">
                            <ul class="elementor-icon-list-items">
                                <li class="elementor-icon-list-item">
                                    <a href="#">

                                        <span class="elementor-icon-list-text">
                                            <font size="5">📧</font> &nbsp;&nbsp; <EMAIL>
                                        </span>
                                    </a>
                                </li>
                                <li class="elementor-icon-list-item">
                                    <a href="tel:+15615302575">

                                        <span class="elementor-icon-list-text">
                                            <font size="5">💬</font> &nbsp;&nbsp; +15615302575
                                        </span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="elementor-element elementor-element-0fe777e e-con-full e-flex e-con e-child"
                    data-id="0fe777e" data-element_type="container">
                    <div class="elementor-element elementor-element-68e6678 elementor-widget elementor-widget-heading"
                        data-id="68e6678" data-element_type="widget" data-widget_type="heading.default">
                        <div class="elementor-widget-container">
                            <h4 class="elementor-heading-title elementor-size-default"><u>Address</u></h4>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-52458ad elementor-align-left elementor-icon-list--layout-traditional elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list"
                        data-id="52458ad" data-element_type="widget" data-widget_type="icon-list.default">
                        <div class="elementor-widget-container">
                            <ul class="elementor-icon-list-items">
                                <li class="elementor-icon-list-item">
                                    <a href="#">

                                        <span class="elementor-icon-list-text">
                                            <font size="5">🌍</font> &nbsp;&nbsp; 21 NE 10th St, Deerfield Beach FL 33441
                                        </span>
                                    </a>
                                </li>
                                <li class="elementor-icon-list-item">
                                    <a href="#">

                                        <span class="elementor-icon-list-text">
                                            <font size="5">🕒</font> &nbsp;&nbsp; Monday → Friday 9am to 5pm
                                        </span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
       
    </div>
   <div data-elementor-type="footer" data-elementor-id="1764"
                class="elementor elementor-1764 elementor-location-footer" data-elementor-post-type="elementor_library">
                <div class="elementor-element elementor-element-78cad96 e-flex e-con-boxed e-con e-parent"
                    data-id="78cad96" data-element_type="container"
                    data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                    <div class="e-con-inner">
                        <div class="elementor-element elementor-element-52abb05 e-con-full e-flex e-con e-child"
                            data-id="52abb05" data-element_type="container">
                            <div class="elementor-element elementor-element-600cf77 elementor-widget elementor-widget-elementskit-heading"
                                data-id="600cf77" data-element_type="widget"
                                data-widget_type="elementskit-heading.default">
                                <div class="elementor-widget-container">
                                    <div class="ekit-wid-con">
                                        <div
                                            class="ekit-heading elementskit-section-title-wraper text_left   ekit_heading_tablet-text_center   ekit_heading_mobile-text_left">
                                            <h3 class="ekit-heading--title elementskit-section-title text_fill">
                                                Ready to transform <span><span>your
                                                        business? </span></span></h3>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-8d50095 elementor-align-left elementor-align--tabletcenter elementor-widget elementor-widget-elementskit-button"
                                data-id="8d50095" data-element_type="widget"
                                data-widget_type="elementskit-button.default">
                                <div class="elementor-widget-container">
                                    <div class="ekit-wid-con">
                                        <div class="ekit-btn-wraper">
                                            <a href="#" class="elementskit-btn  whitespace--normal" id="">
                                                Learn More <i class="icon icon-right-arrow"></i> </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-2c2a580 e-con-full e-flex e-con e-child"
                            data-id="2c2a580" data-element_type="container">
                            <div class="elementor-element elementor-element-73e9142 e-con-full e-flex e-con e-child"
                                data-id="73e9142" data-element_type="container">
                                <div class="elementor-element elementor-element-bc9c29b elementor-widget elementor-widget-heading"
                                    data-id="bc9c29b" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h4 class="elementor-heading-title elementor-size-default">Connect with
                                            us</h4>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-6d0c3bb elementor-align-left elementor-icon-list--layout-traditional elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list"
                                    data-id="6d0c3bb" data-element_type="widget" data-widget_type="icon-list.default">
                                    <div class="elementor-widget-container">
                                        <ul class="elementor-icon-list-items">
                                            <li class="elementor-icon-list-item">
                                                <a href="mailto:<EMAIL>">
                                                    <span class="elementor-icon-list-text">
                                                        <font size="5">📧</font> &nbsp;&nbsp;
                                                        <EMAIL>
                                                    </span>
                                                </a>
                                            </li>
                                            <li class="elementor-icon-list-item">
                                                <a href="tel:+15615302575">
                                                    <span class="elementor-icon-list-text">
                                                        <font size="5">💬</font> &nbsp;&nbsp; +15615302575
                                                    </span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-6785420 e-con-full e-flex e-con e-child"
                                data-id="6785420" data-element_type="container">
                                <div class="elementor-element elementor-element-e923f1d elementor-widget elementor-widget-heading"
                                    data-id="e923f1d" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h4 class="elementor-heading-title elementor-size-default">Address</h4>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-7bef88b elementor-align-left elementor-icon-list--layout-traditional elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list"
                                    data-id="7bef88b" data-element_type="widget" data-widget_type="icon-list.default">
                                    <div class="elementor-widget-container">
                                        <ul class="elementor-icon-list-items">
                                            <li class="elementor-icon-list-item">
                                                <a href="#">

                                                    <span class="elementor-icon-list-text">
                                                        <font size="5">🌍</font> &nbsp;&nbsp; 21 NE 10th St Deerfield Beach Florida 33441
                                                    </span>
                                                </a>
                                            </li>
                                            <li class="elementor-icon-list-item">
                                                <a href="#">

                                                    <span class="elementor-icon-list-text">
                                                        <font size="5">🕒</font> &nbsp;&nbsp; Monday → Friday
                                                        9am to 5pm
                                                    </span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-9b7cd60 e-con-full e-flex e-con e-child"
                                data-id="9b7cd60" data-element_type="container">
                                <div class="elementor-element elementor-element-e3957e2 elementor-widget elementor-widget-heading"
                                    data-id="e3957e2" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h4 class="elementor-heading-title elementor-size-default"> Join our
                                            newsletter</h4>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-60dd47a elementor-button-align-stretch elementor-widget elementor-widget-form"
                                    data-id="60dd47a" data-element_type="widget"
                                    data-settings="{&quot;step_next_label&quot;:&quot;Next&quot;,&quot;step_previous_label&quot;:&quot;Previous&quot;,&quot;button_width&quot;:&quot;20&quot;,&quot;button_width_mobile&quot;:&quot;20&quot;,&quot;step_type&quot;:&quot;number_text&quot;,&quot;step_icon_shape&quot;:&quot;circle&quot;}"
                                    data-widget_type="form.default">
                                    <div class="elementor-widget-container">
                                        <form class="elementor-form" method="post" name="New Form">
                                            <input type="hidden" name="post_id" value="1764" />
                                            <input type="hidden" name="form_id" value="60dd47a" />
                                            <input type="hidden" name="referer_title" value="Services" />

                                            <input type="hidden" name="queried_id" value="2433" />

                                            <div class="elementor-form-fields-wrapper elementor-labels-above">
                                                <div
                                                    class="elementor-field-type-email elementor-field-group elementor-column elementor-field-group-email elementor-col-80 elementor-sm-80 elementor-field-required">
                                                    <input size="1" type="email" name="form_fields[email]"
                                                        id="form-field-email"
                                                        class="elementor-field elementor-size-sm  elementor-field-textual"
                                                        placeholder="Email Address" required="required">
                                                </div>
                                                <div
                                                    class="elementor-field-group elementor-column elementor-field-type-submit elementor-col-20 e-form__buttons elementor-sm-20">
                                                    <button class="elementor-button elementor-size-sm" type="submit">
                                                        <span class="elementor-button-content-wrapper">
                                                            <span class="elementor-button-icon">
                                                                <i aria-hidden="true" class="icon icon-right-arrow"></i>
                                                                <span class="elementor-screen-only">Submit</span>
                                                            </span>
                                                        </span>
                                                    </button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-b9f61ee e-con-full e-flex e-con e-child"
                                data-id="b9f61ee" data-element_type="container">
                                <div class="elementor-element elementor-element-8737e81 elementor-widget elementor-widget-heading"
                                    data-id="8737e81" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h4 class="elementor-heading-title elementor-size-default">Follow us
                                        </h4>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-265915a e-grid e-con-full e-con e-child"
                                    data-id="265915a" data-element_type="container">
                                    <div class="elementor-element elementor-element-5a77c23 elementor-widget elementor-widget-elementskit-social-media"
                                        data-id="5a77c23" data-element_type="widget"
                                        data-widget_type="elementskit-social-media.default">
                                        <div class="elementor-widget-container">
                                            <div class="ekit-wid-con">
                                                <ul class="ekit_social_media">
                                                    <li class="elementor-repeater-item-633e4a1">
                                                        <a href="https://www.facebook.com/profile.php?id=61575209545205" aria-label="Facebook" target="_blank" class="">
                                                            Facebook </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="elementor-element elementor-element-652d726 elementor-widget elementor-widget-elementskit-social-media"
                                        data-id="652d726" data-element_type="widget"
                                        data-widget_type="elementskit-social-media.default">
                                        <div class="elementor-widget-container">
                                            <div class="ekit-wid-con">
                                                <ul class="ekit_social_media">
                                                    <li class="elementor-repeater-item-55fe18f">
                                                        <a href="https://x.com/tomsantella" aria-label="Twitter" target="_blank"  class="">
                                                            Twitter </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="elementor-element elementor-element-c8b0b41 elementor-widget elementor-widget-elementskit-social-media"
                                        data-id="c8b0b41" data-element_type="widget"
                                        data-widget_type="elementskit-social-media.default">
                                        <div class="elementor-widget-container">
                                            <div class="ekit-wid-con">
                                                <ul class="ekit_social_media">
                                                    <li class="elementor-repeater-item-55fe18f">
                                                        <a href="https://www.instagram.com/nextlevelgrowthpartners/?hl=en" aria-label="Instagram" target="_blank"  class="">
                                                            Instagram </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="elementor-element elementor-element-7bde8dc elementor-widget elementor-widget-elementskit-social-media"
                                        data-id="7bde8dc" data-element_type="widget"
                                        data-widget_type="elementskit-social-media.default">
                                        <div class="elementor-widget-container">
                                            <div class="ekit-wid-con">
                                                <ul class="ekit_social_media">
                                                    <li class="elementor-repeater-item-55fe18f">
                                                        <a href="https://www.linkedin.com/company/next-level-growth-partners/" aria-label="LinkedIn" target="_blank"  class="">
                                                            LinkedIn </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-dc0f0af e-con-full e-flex e-con e-child"
                            data-id="dc0f0af" data-element_type="container">
                            <div class="elementor-element elementor-element-49dd403 elementor-widget elementor-widget-theme-site-title elementor-widget-heading"
                                data-id="49dd403" data-element_type="widget" data-widget_type="theme-site-title.default"
                                style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                                <div class="elementor-widget-container" style="flex: 1; min-width: 200px;">
                                    <p style="margin: 0; font-size: 14px;">
                                        <a href="privacy-policy.html"
                                            style="margin-right: 1rem; text-decoration: underline;">Privacy Policy</a>
                                        <a href="terms.html" style="text-decoration: underline;">Terms of Service</a>
                                    </p>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-49dd403 elementor-widget elementor-widget-theme-site-title elementor-widget-heading"
                                data-id="49dd403" data-element_type="widget" data-widget_type="theme-site-title.default"
                                style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                                <div class="elementor-widget-container"
                                    style="flex: 1; min-width: 200px; text-align: right;">
                                    <h2 class="elementor-heading-title elementor-size-default"
                                        style="margin: 0; font-size: 14px;">
                                        <a href="index.html">Copyright © 2025 Next Level Growth Partners </a>
                                    </h2>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

    <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script>
    <script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/hello-elementor\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
    <script>
        const lazyloadRunObserver = () => {
            const lazyloadBackgrounds = document.querySelectorAll(`.e-con.e-parent:not(.e-lazyloaded)`);
            const lazyloadBackgroundObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        let lazyloadBackground = entry.target;
                        if (lazyloadBackground) {
                            lazyloadBackground.classList.add('e-lazyloaded');
                        }
                        lazyloadBackgroundObserver.unobserve(entry.target);
                    }
                });
            }, { rootMargin: '200px 0px 200px 0px' });
            lazyloadBackgrounds.forEach((lazyloadBackground) => {
                lazyloadBackgroundObserver.observe(lazyloadBackground);
            });
        };
        const events = [
            'DOMContentLoaded',
            'elementor/lazyload/observe',
        ];
        events.forEach((event) => {
            document.addEventListener(event, lazyloadRunObserver);
        });
    </script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/themes/hello-elementor/assets/js/hello-frontend.min.js?ver=3.3.0"
        id="hello-theme-frontend-js"></script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/lib/smartmenus/jquery.smartmenus.min.js?ver=1.2.1"
        id="smartmenus-js"></script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/libs/framework/assets/js/frontend-script.js?ver=3.4.0"
        id="elementskit-framework-js-frontend-js"></script>
    <script id="elementskit-framework-js-frontend-js-after">
        var elementskit = {
            resturl: 'https://brandmode.1onestrong.com/wp-json/elementskit/v1/',
        }


    </script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/js/widget-scripts.js?ver=3.4.0"
        id="ekit-widget-scripts-js"></script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/js/webpack-pro.runtime.min.js?ver=3.27.2"
        id="elementor-pro-webpack-runtime-js"></script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.27.3"
        id="elementor-webpack-runtime-js"></script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.27.3"
        id="elementor-frontend-modules-js"></script>
    <script src="https://brandmode.1onestrong.com/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6"
        id="wp-hooks-js"></script>
    <script src="https://brandmode.1onestrong.com/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6"
        id="wp-i18n-js"></script>
    <script id="wp-i18n-js-after">
        wp.i18n.setLocaleData({ 'text direction\u0004ltr': ['ltr'] });
    </script>
    <script id="elementor-pro-frontend-js-before">
        var ElementorProFrontendConfig = { "ajaxurl": "https:\/\/brandmode.1onestrong.com\/wp-admin\/admin-ajax.php", "nonce": "030e58e6ef", "urls": { "assets": "https:\/\/brandmode.1onestrong.com\/wp-content\/plugins\/elementor-pro\/assets\/", "rest": "https:\/\/brandmode.1onestrong.com\/wp-json\/" }, "settings": { "lazy_load_background_images": true }, "popup": { "hasPopUps": false }, "shareButtonsNetworks": { "facebook": { "title": "Facebook", "has_counter": true }, "twitter": { "title": "Twitter" }, "linkedin": { "title": "LinkedIn", "has_counter": true }, "pinterest": { "title": "Pinterest", "has_counter": true }, "reddit": { "title": "Reddit", "has_counter": true }, "vk": { "title": "VK", "has_counter": true }, "odnoklassniki": { "title": "OK", "has_counter": true }, "tumblr": { "title": "Tumblr" }, "digg": { "title": "Digg" }, "skype": { "title": "Skype" }, "stumbleupon": { "title": "StumbleUpon", "has_counter": true }, "mix": { "title": "Mix" }, "telegram": { "title": "Telegram" }, "pocket": { "title": "Pocket", "has_counter": true }, "xing": { "title": "XING", "has_counter": true }, "whatsapp": { "title": "WhatsApp" }, "email": { "title": "Email" }, "print": { "title": "Print" }, "x-twitter": { "title": "X" }, "threads": { "title": "Threads" } }, "facebook_sdk": { "lang": "en_US", "app_id": "" }, "lottie": { "defaultAnimationUrl": "https:\/\/brandmode.1onestrong.com\/wp-content\/plugins\/elementor-pro\/modules\/lottie\/assets\/animations\/default.json" } };
    </script>
    <script src="https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/js/frontend.min.js?ver=3.27.2"
        id="elementor-pro-frontend-js"></script>
    <script src="https://brandmode.1onestrong.com/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3"
        id="jquery-ui-core-js"></script>
    <script id="elementor-frontend-js-before">
        var elementorFrontendConfig = { "environmentMode": { "edit": false, "wpPreview": false, "isScriptDebug": false }, "i18n": { "shareOnFacebook": "Share on Facebook", "shareOnTwitter": "Share on Twitter", "pinIt": "Pin it", "download": "Download", "downloadImage": "Download image", "fullscreen": "Fullscreen", "zoom": "Zoom", "share": "Share", "playVideo": "Play Video", "previous": "Previous", "next": "Next", "close": "Close", "a11yCarouselPrevSlideMessage": "Previous slide", "a11yCarouselNextSlideMessage": "Next slide", "a11yCarouselFirstSlideMessage": "This is the first slide", "a11yCarouselLastSlideMessage": "This is the last slide", "a11yCarouselPaginationBulletMessage": "Go to slide" }, "is_rtl": false, "breakpoints": { "xs": 0, "sm": 480, "md": 768, "lg": 1025, "xl": 1440, "xxl": 1600 }, "responsive": { "breakpoints": { "mobile": { "label": "Mobile Portrait", "value": 767, "default_value": 767, "direction": "max", "is_enabled": true }, "mobile_extra": { "label": "Mobile Landscape", "value": 880, "default_value": 880, "direction": "max", "is_enabled": false }, "tablet": { "label": "Tablet Portrait", "value": 1024, "default_value": 1024, "direction": "max", "is_enabled": true }, "tablet_extra": { "label": "Tablet Landscape", "value": 1200, "default_value": 1200, "direction": "max", "is_enabled": false }, "laptop": { "label": "Laptop", "value": 1366, "default_value": 1366, "direction": "max", "is_enabled": true }, "widescreen": { "label": "Widescreen", "value": 2400, "default_value": 2400, "direction": "min", "is_enabled": false } }, "hasCustomBreakpoints": true }, "version": "3.27.3", "is_static": false, "experimentalFeatures": { "e_font_icon_svg": true, "additional_custom_breakpoints": true, "container": true, "e_swiper_latest": true, "e_onboarding": true, "theme_builder_v2": true, "hello-theme-header-footer": true, "home_screen": true, "landing-pages": true, "nested-elements": true, "editor_v2": true, "link-in-bio": true, "floating-buttons": true }, "urls": { "assets": "https:\/\/brandmode.1onestrong.com\/wp-content\/plugins\/elementor\/assets\/", "ajaxurl": "https:\/\/brandmode.1onestrong.com\/wp-admin\/admin-ajax.php", "uploadUrl": "https:\/\/brandmode.1onestrong.com\/wp-content\/uploads" }, "nonces": { "floatingButtonsClickTracking": "a172e92387" }, "swiperClass": "swiper", "settings": { "page": [], "editorPreferences": [] }, "kit": { "body_background_background": "classic", "active_breakpoints": ["viewport_mobile", "viewport_tablet", "viewport_laptop"], "global_image_lightbox": "yes", "lightbox_enable_counter": "yes", "lightbox_enable_fullscreen": "yes", "lightbox_enable_zoom": "yes", "lightbox_enable_share": "yes", "lightbox_title_src": "title", "lightbox_description_src": "description", "hello_header_logo_type": "logo", "hello_header_menu_layout": "horizontal", "hello_footer_logo_type": "logo" }, "post": { "id": 2672, "title": "Contact%20%E2%80%93%20Brandmode", "excerpt": "", "featuredImage": false } };
    </script>
    <script src="https://brandmode.1onestrong.com/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.27.3"
        id="elementor-frontend-js"></script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/elementor-pro/assets/js/elements-handlers.min.js?ver=3.27.2"
        id="pro-elements-handlers-js"></script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/js/animate-circle.min.js?ver=3.4.0"
        id="animate-circle-js"></script>
    <script id="elementskit-elementor-js-extra">
        var ekit_config = { "ajaxurl": "https:\/\/brandmode.1onestrong.com\/wp-admin\/admin-ajax.php", "nonce": "be08e23588" };
    </script>
    <script
        src="https://brandmode.1onestrong.com/wp-content/plugins/elementskit-lite/widgets/init/assets/js/elementor.js?ver=3.4.0"
        id="elementskit-elementor-js"></script>

    <script>
        // Contact form submission handler
        document.addEventListener('DOMContentLoaded', function() {
            // Handle main contact form
            const mainContactForm = document.querySelector('form.elementor-form[name="New Form"]');
            if (mainContactForm) {
                mainContactForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    handleFormSubmission(this, 'contact_form', 'contact_page_main');
                });
            }

            // Handle newsletter form
            const newsletterForms = document.querySelectorAll('form.elementor-form');
            newsletterForms.forEach(form => {
                if (form !== mainContactForm) {
                    form.addEventListener('submit', function(e) {
                        e.preventDefault();
                        handleNewsSubmission(this, 'newsletter_signup', 'contact_page_newsletter');
                    });
                }
            });
        });

        function handleFormSubmission(form, formType, source) {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Submitting...';
            submitBtn.disabled = true;

            // Collect form data
            const formData = new FormData(form);
            let data = {
                form_type: formType,
                source: source,
                timestamp: new Date().toISOString(),
                page_url: window.location.href,
                user_agent: navigator.userAgent
            };

                data = {
                    ...data,
                    first_name: formData.get('form_fields[first_name]'),
                    last_name: formData.get('form_fields[last_name]'),
                    email: formData.get('form_fields[email]'),
                    phone: formData.get('form_fields[phone]'),
                    company: formData.get('form_fields[company]'),
                    service_interest: formData.get('form_fields[service]'),
                    referral_source: formData.get('form_fields[referral]'),
                    message: formData.get('form_fields[message]'),
                    sms_consent: formData.get('form_fields[sms_consent]') === 'yes',
                    terms_consent: formData.get('form_fields[terms]') === 'yes',
                    company_size: formData.get('form_fields[company_size]'),
                     role: formData.get('form_fields[role]'),
                };
           


            const webhookTestUrl = 'https://automate.axonflash.com/webhook/form-submit';
const webhookProdUrl = window.WEBHOOK_CONFIG
    ? window.WEBHOOK_CONFIG.CONTACT_FORM_WEBHOOK
    : 'https://automate.axonflash.com/webhook/demo-form-submission';

fetch(webhookTestUrl, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
})
.then(testResponse => {
    if (!testResponse.ok) throw new Error('Test webhook failed');
    return testResponse.json();
})
.then(() => {
    // After test webhook succeeds, send to production webhook
    return fetch(webhookProdUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    });
})
.then(prodResponse => {
    if (!prodResponse.ok) throw new Error('Production webhook failed');
    return prodResponse.json();
})
.then(() => {
    submitBtn.innerHTML = '<i class="fas fa-check mr-2"></i> Success!';
    setTimeout(() => {
        if (formType === 'contact_form') {
            alert('Thank you! Your message has been sent successfully. We will contact you soon.');
        }
        form.reset();
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 1500);
})
.catch(error => {
    console.error('Error:', error);
    submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i> Error - Try Again';
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
    // Optionally: alert('There was an error submitting your request.');
});

        }

        function handleNewsSubmission(form, formType, source) {
            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Submitting...';
            submitBtn.disabled = true;

            // Collect form data
            const formData = new FormData(form);
            let data = {
                form_type: formType,
                source: source,
                timestamp: new Date().toISOString(),
                page_url: window.location.href,
                user_agent: navigator.userAgent
            };

            if (formType === 'newsletter_signup') {
                data = {
                    ...data,
                    email: formData.get('form_fields[email]')
                };
            }


            const webhookTestUrl = 'https://automate.axonflash.com/webhook/newsletter';


fetch(webhookTestUrl, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
})
.then(testResponse => {
    if (!testResponse.ok) throw new Error('Test webhook failed');
    return testResponse.json();
})

.then(() => {
    submitBtn.innerHTML = '<i class="fas fa-check mr-2"></i> Success!';
    setTimeout(() => {
        if (formType === 'contact_form') {
            alert('Thank you! Your message has been sent successfully. We will contact you soon.');
        }
        form.reset();
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 1500);
})
.catch(error => {
    console.error('Error:', error);
    submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i> Error - Try Again';
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 3000);
    // Optionally: alert('There was an error submitting your request.');
});

        }
    </script>



</body>

</html>