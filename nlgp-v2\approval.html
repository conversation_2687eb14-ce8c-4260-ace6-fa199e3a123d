<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Frappe Google Sheet Integration</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    .frappe-bg { background-color: #f5f7fa; }
    .sidebar-bg { background-color: #2c3e50; }
    .header-bg {
      background-color: #ffffff;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .card-bg {
      background-color: #ffffff;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    .table-header { background-color: #f7fafc; color: #36414c; }
    .btn-primary { background-color: #5e64ff; color: white; }
    .btn-primary:hover { background-color: #4a50e0; }
    .text-muted { color: #8d99a6; }
    .nav-link { color: #d1d8dd; }
    .nav-link:hover { color: white; }
    .active-link { color: white; background-color: rgba(255,255,255,0.1); }
    .table-row:hover { background-color: #f8f9fa; }
  </style>
</head>
<body class="frappe-bg min-h-screen flex">
  <div class="sidebar-bg w-64 min-h-screen text-white hidden md:block">
    <div class="p-4 border-b border-gray-700">
      <h1 class="text-xl font-semibold flex items-center">
        <i class="fas fa-cube mr-2"></i> Frappe App
      </h1>
    </div>
    <nav class="mt-4">
      <div class="px-4 py-2 text-xs uppercase tracking-wider text-gray-400">Modules</div>
      <a href="#" class="nav-link active-link flex items-center px-4 py-2">
        <i class="fas fa-table mr-3"></i> Data Tables
      </a>
      <a href="#" class="nav-link flex items-center px-4 py-2">
        <i class="fas fa-chart-bar mr-3"></i> Reports
      </a>
      <a href="#" class="nav-link flex items-center px-4 py-2">
        <i class="fas fa-cog mr-3"></i> Settings
      </a>
      <div class="px-4 py-2 text-xs uppercase tracking-wider text-gray-400 mt-4">Tools</div>
      <a href="#" class="nav-link flex items-center px-4 py-2">
        <i class="fas fa-calendar mr-3"></i> Calendar
      </a>
      <a href="#" class="nav-link flex items-center px-4 py-2">
        <i class="fas fa-users mr-3"></i> Users
      </a>
    </nav>
  </div>

  <div class="flex-1 flex flex-col">
    <header class="header-bg py-3 px-6 flex items-center justify-between">
      <div class="flex items-center">
        <button class="md:hidden mr-4 text-gray-600">
          <i class="fas fa-bars"></i>
        </button>
        <h2 class="text-lg font-semibold text-gray-800">Data Tables</h2>
      </div>
      <div class="flex items-center space-x-4">
        <div class="relative">
          <input type="text" placeholder="Search..." class="pl-8 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
          <i class="fas fa-search absolute left-2 top-3 text-gray-400"></i>
        </div>
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white">
            <i class="fas fa-user"></i>
          </div>
          <span class="text-sm hidden md:inline">Admin</span>
        </div>
      </div>
    </header>

    <main class="flex-1 p-6">
      <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h3 class="text-xl font-semibold text-gray-800">Google Sheet Viewer</h3>
          <p class="text-muted text-sm">Live view and approval of data</p>
        </div>
      </div>

      <div class="card-bg overflow-hidden">
        <div id="loader" class="text-sm text-center text-gray-500 py-3">Loading...</div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="table-header">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"><input type="checkbox"></th>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Email</th>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Phone</th>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Company Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Category</th>
                <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Action</th>
              </tr>
            </thead>
            <tbody id="data-table-body" class="bg-white divide-y divide-gray-200">
              <!-- Dynamic rows go here -->
            </tbody>
          </table>
        </div>
      </div>
    </main>
  </div>

  <script>
    const sheetAPI = 'https://sheets.googleapis.com/v4/spreadsheets/1uGmiqozxfHfsklaNE3DMSnjzT5B4vCHEbXAao38R4Eg/values/Sheet1?alt=json&key=AIzaSyDw0WDpU5A1cujeUD4qgIMPP1IfkGmBxnE';
    const updateAPI = 'https://script.google.com/macros/s/AKfycby1a_oZNyHZdiN5OKu6VGbbQgh97PzAMMVPbhd4U9SwU-r7rMlMNrGnulLifqrzg_LfKw/exec';

    async function fetchData() {
      const tbody = document.getElementById('data-table-body');
      const loader = document.getElementById('loader');
      tbody.innerHTML = '';
      loader.style.display = 'block';

      try {
        const res = await fetch(sheetAPI);
        const data = await res.json();
        const headers = data.values[0];
        const rows = data.values.slice(1).map(row => {
          const obj = {};
          headers.forEach((h, i) => obj[h] = row[i] || '');
          return obj;
        }).filter(row => row.Status !== 'Approved');

        loader.style.display = 'none';
        populateTable(rows);
      } catch (err) {
        loader.textContent = '❌ Error loading data';
      }
    }

    function populateTable(rows) {
      const tbody = document.getElementById('data-table-body');
      rows.forEach(row => {
        const tr = document.createElement('tr');
        tr.classList.add('table-row');
        tr.innerHTML = `
          <td class="px-6 py-4"><input type="checkbox"></td>
          <td class="px-6 py-4">${row.Name || '-'}</td>
          <td class="px-6 py-4">${row.Email || '-'}</td>
          <td class="px-6 py-4">${row.Phone || '-'}</td>
          <td class="px-6 py-4">${row.Status || '-'}</td>
          <td class="px-6 py-4">${row['Company Name'] || '-'}</td>
          <td class="px-6 py-4">${row['Company Category'] || '-'}</td>
          <td class="px-6 py-4">
            <button class="approve-btn bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-sm">Approve</button>
          </td>
        `;
        const btn = tr.querySelector('.approve-btn');
        btn.addEventListener('click', () => approveRow(btn, row));
        tbody.appendChild(tr);
      });
    }

    async function approveRow(button, row) {
      button.disabled = true;
      button.textContent = '⏳ Approving...';
      try {
        await fetch(updateAPI, {
          method: 'POST',
          mode: 'no-cors',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: row.Email, status: 'Approved' })
        });
        button.textContent = '✅ Approved';
        setTimeout(() => fetchData(), 2000);
      } catch (err) {
        button.disabled = false;
        button.textContent = '❌ Error';
      }
    }

    fetchData();
  </script>
</body>
</html>
