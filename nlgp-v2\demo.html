
<!DOCTYPE html><html  lang="en" data-capo=""><head><meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>AI Lead Follow-Up Automation | Next Level Growth Partner</title>
<link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="anonymous">
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Lato:100,200,300,400,500,600,700,800,900%7COpen%20Sans:100,200,300,400,500,600,700,800,900%7CMontserrat:100,200,300,400,500,600,700,800,900%7CRed%20Hat%20Display:100,200,300,400,500,600,700,800,900%7CRoboto:100,200,300,400,500,600,700,800,900%7C'Roboto':100,200,300,400,500,600,700,800,900%7CAkshar:100,200,300,400,500,600,700,800,900%7CPoppins:100,200,300,400,500,600,700,800,900%7CPlayfair%20Display:100,200,300,400,500,600,700,800,900%7C'Poppins':100,200,300,400,500,600,700,800,900%7C'Playfair%20Display':100,200,300,400,500,600,700,800,900&display=swap" media="print" onload="this.media='all'">
<style type="text/css"> 

 :root{ --primary: #1e40af;
--secondary: #3b82f6;
--white: #FFFFFF;
--gray: #e2e8f0;
--black: #1e293b;
--red: #ef4444;
--orange: #1e40af;
--yellow: #fbbf24;
--green: #10b981;
--teal: #06b6d4;
--malibu: #3b82f6;
--indigo: #6366f1;
--purple: #8b5cf6;
--pink: #ec4899;
--transparent: transparent;
--overlay: rgba(30, 64, 175, 0.15);
--color-ljn1ecb0: #1e40af;
--color-ljn1zn5v: #f0f9ff;
--color-ljn2bjhz: #3b82f6;
--color-ljn4i7ro: #dbeafe;
--color-ljn6qg27: rgba(30,64,175,0.91);
--color-lkbhfki1: #1e293b;
--color-lkbhik22: #3b82f6;
--color-lkbjs320: #f8fafc;
--color-lkbplv6z: #ffffff;
--color-lkd2tix4: #f0f9ff;
--color-lkcyc6g7: #f8fafc;
--color-lkd4di7k: rgba(59,130,246,0.15);
--color-lkd7d1vg: #f0f9ff;
--color-lkvtf9he: #2563eb;
--color-lle2s7f6: #1d4ed8;
--color-lkrcvoml: #1e40af;
--color-m1tdsdiu: #FFFFFF;
--color-m1tdv4ke: #f1f5f9;
--color-m1tdwbyo: #1e293b;
--arial: 'Arial';
--lato: 'Lato';
--open-sans: 'Open Sans';
--montserrat: 'Montserrat';
--roboto: 'Roboto';
--red-hat-display: 'Red Hat Display';
--akshar: 'Akshar';
--poppins: 'Poppins';
--playfair-display: 'Playfair Display';
--headlinefont: 'Roboto';
--contentfont: 'Roboto';
--text-color: #000000;
--link-color: #188bf6; } .bg-fixed{bottom:0;top:0;left:0;right:0;position:fixed;overflow:auto;background-color:var(--color-m1tdwbyo)} 
      
      .drop-zone-draggable .hl_main_popup{padding:0;margin-top:0;border-color:var(--transparent);border-width:1px;border-style:solid;background-color:var(--white)}
      
      

      .drop-zone-draggable .row-_2Hy4yY6Hj{margin-top:0;margin-bottom:0}
      .drop-zone-draggable .row-_2Hy4yY6Hj{padding:0;background-color:var(--transparent);border-color:var(--black);border-width:2px;border-style:solid;width:100%}
      
      
.drop-zone-draggable .col-ONIwjdczOL{padding:0;background-color:var(--transparent);width:42.6%;border-color:var(--black);border-width:2px;border-style:solid;margin-top:0;margin-bottom:0}

      .drop-zone-draggable .custom-code-CoC46R6UV7{margin:0}
      
      
      
#hl_main_popup{padding:0;margin-top:0;border-color:var(--transparent);border-width:1px;border-style:solid;background-color:var(--white);width:960px}@media screen and (min-width:0px) and (max-width:480px){#hl_main_popup{width:380px!important}} #col-ONIwjdczOL>.inner{flex-direction:column;justify-content:center;align-items:inherit;flex-wrap:nowrap}  
 /* ---- Header styles with modern blue theme and animations ----- */
:root{--white:#FFFFFF;--black:#1e293b;--transparent:transparent;--overlay:rgba(30, 64, 175, 0.15);--color-lkbhik22:#3b82f6;--color-m1tdsdiu:#FFFFFF}

/* Modern animations and transitions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Glassmorphism effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(30, 64, 175, 0.1);
}

/* Modern mobile improvements */
@media (max-width: 768px) {
    .hl_page-preview--content .section-m0AmIUbB7,
    .hl_page-preview--content .section-wAGehRwoGr,
    .hl_page-preview--content .section-sfVzbZHQjy,
    .hl_page-preview--content .section-ay3GWFejuN,
    .hl_page-preview--content .section-kr3hxqv4nG {
        padding: 40px 15px;
        margin: 0;
    }

    .hl_page-preview--content .cbutton-dDRV8gIyYx,
    .hl_page-preview--content .cbutton-XOKFNf57PF,
    .hl_page-preview--content .cbutton-cX3vuxtbMw {
        padding: 16px 32px;
        font-size: 16px;
        border-radius: 40px;
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.25);
    }

    .hl_page-preview--content .cbutton-dDRV8gIyYx:hover,
    .hl_page-preview--content .cbutton-XOKFNf57PF:hover,
    .hl_page-preview--content .cbutton-cX3vuxtbMw:hover {
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.35);
    }
}

/* Enhanced scroll animations */
@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Floating animation for icons */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.floating-element {
    animation: float 3s ease-in-out infinite;
}

/* Gradient text effect */
.gradient-text {
    background: linear-gradient(135deg, #3b82f6, #1e40af);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}.hl_page-preview--content .row-xmdyLrDPJd,.hl_page-preview--content .section-m0AmIUbB7{margin-top:0;margin-bottom:0;background-color:var(--transparent);border-color:var(--black);border-width:2px;border-style:solid}.hl_page-preview--content .section-m0AmIUbB7{padding:30px 0;margin-right:0}.hl_page-preview--content .row-xmdyLrDPJd{padding:0;width:100%}.hl_page-preview--content .col-qVAwIKxF0h{width:70.4%}.hl_page-preview--content .col-qVAwIKxF0h .inner{width:70.4%;margin-bottom:0}.hl_page-preview--content .cnav-menu-ZXRIR7fv2T,.hl_page-preview--content .col-qVAwIKxF0h .inner,.hl_page-preview--content .col-rUj1LnNf1n .inner{padding:0;background-color:var(--transparent);border-color:var(--black);border-width:2px;border-style:solid;margin-top:0}.hl_page-preview--content .cnav-menu-ZXRIR7fv2T{font-family:var(--poppins);mobile-background-color:var(--overlay);color:var(--white);bold-text-color:var(--white);italic-text-color:var(--white);underline-text-color:var(--white);icon-color:var(--white);secondary-color:var(--white);nav-menu-item-hover-background-color:var(--transparent);line-height:1.3em;text-transform:none;letter-spacing:0;text-align:left;nav-menu-item-spacing-x:20px;nav-menu-item-spacing-y:5px;dropdown-background:var(--white);dropdown-text-color:var(--black);dropdown-hover-color:var(--black);dropdown-item-spacing:10px}.hl_page-preview--content .col-rUj1LnNf1n{width:29.6%}.hl_page-preview--content .col-rUj1LnNf1n .inner{width:29.6%;margin-bottom:0}.hl_page-preview--content .button-dDRV8gIyYx{margin-top:0;margin-bottom:0;text-align:center}.hl_page-preview--content .cbutton-dDRV8gIyYx{font-family:var(--poppins);background:linear-gradient(135deg, #3b82f6, #1e40af);color:var(--color-m1tdsdiu);secondary-color:var(--white);text-decoration:none;padding:14px 40px;font-weight:700;border-color:var(--transparent);border-width:1px;border-style:solid;letter-spacing:0;text-transform:none;text-shadow:0 0 0 transparent;width:auto%;transition:all 0.3s ease;box-shadow:0 4px 15px rgba(59, 130, 246, 0.3);border-radius:50px;position:relative;overflow:hidden}

.hl_page-preview--content .cbutton-dDRV8gIyYx:hover{transform:translateY(-2px);box-shadow:0 8px 25px rgba(59, 130, 246, 0.4);background:linear-gradient(135deg, #2563eb, #1d4ed8)}

.hl_page-preview--content .cbutton-dDRV8gIyYx::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);transition:left 0.5s;z-index:1}

.hl_page-preview--content .cbutton-dDRV8gIyYx:hover::before{left:100%}#section-m0AmIUbB7>.inner{max-width:1170px}#col-qVAwIKxF0h>.inner{flex-direction:column;justify-content:center;align-items:inherit;flex-wrap:nowrap}.--mobile #nav-menu-ZXRIR7fv2T .nav-menu{font-size:20px;font-weight:undefined}#nav-menu-ZXRIR7fv2T .nav-menu,.--mobile #nav-menu-ZXRIR7fv2T .nav-menu .dropdown-menu .dropdown-item{font-size:18px;font-weight:undefined}#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu-body{background:var(--overlay)}#nav-menu-ZXRIR7fv2T .nav-menu li.nav-menu-item{display:flex;align-items:center;white-space:nowrap}#nav-menu-ZXRIR7fv2T .items-cart-active{color:#155eef}#nav-menu-ZXRIR7fv2T .items-cart{color:undefined}#nav-menu-ZXRIR7fv2T .nav-menu .nav-menu-item a,#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu .nav-menu-item a{color:var(--white);cursor:pointer;height:inherit;display:flex;align-items:center;padding:5px 20px;transition:all .3s ease}#nav-menu-ZXRIR7fv2T .nav-menu .nav-menu-item:hover a{color:var(--white);background:var(--transparent)}#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu .nav-menu-item:hover a,#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu-body .nav-menu .nav-menu-item:hover{color:var(--white)}#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu{font-size:20px;font-family:var(--poppins);font-weight:undefined}#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu-body .close-menu{color:var(--white);font-size:20px;font-weight:600}#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu-body .nav-menu .nav-menu-item{color:var(--white);text-align:left;transition:all .3s ease;font-size:18px;font-weight:undefined}#nav-menu-ZXRIR7fv2T .nav-menu .dropdown-menu .dropdown-item{font-size:16px;text-align:left;font-weight:undefined}#nav-menu-ZXRIR7fv2T .nav-menu .dropdown-menu .dropdown-item a,#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu .nav-dropdown-menu .nav-menu-item:hover a{color:var(--black);background:var(--white);padding:10px;transition:all .3s ease}#nav-menu-ZXRIR7fv2T .nav-menu .dropdown-menu .dropdown-item:hover a,#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu-body .nav-dropdown-menu .nav-menu-item a,#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu-body .nav-dropdown-menu .nav-menu-item:hover a{color:var(--black)}#nav-menu-popup.nav-menu-ZXRIR7fv2T .nav-menu-body .nav-dropdown-menu .nav-menu-item{background:var(--white);font-size:18px;font-weight:undefined}#nav-menu-ZXRIR7fv2T .nav-menu-mobile span::before{cursor:pointer;color:var(--white);content:"";font-family:"Font Awesome 5 Free";font-size:20px;font-weight:600}@media screen and (min-width:0px) and (max-width:480px){.nav-menu-ZXRIR7fv2T .branding .title{font-size:20px;line-height:1.3em;font-weight:undefined}#nav-menu-ZXRIR7fv2T .hl-autocomplete-results li{font-size:20px}}@media screen and (min-width:481px) and (max-width:10000px){.nav-menu-ZXRIR7fv2T .branding .title{font-size:18px;line-height:1.3em;font-weight:undefined}#nav-menu-ZXRIR7fv2T .hl-autocomplete-results li{font-size:18px}}#nav-menu-ZXRIR7fv2T strong{font-weight:700}#nav-menu-ZXRIR7fv2T em,#nav-menu-ZXRIR7fv2T strong,#nav-menu-ZXRIR7fv2T u{color:var(--white)!important}#nav-menu-ZXRIR7fv2T .hl-autocomplete-input{border:1px solid #cacaca;margin:1px;border-radius:16px}#nav-menu-ZXRIR7fv2T .hl-autocomplete{font-family:inherit}#nav-menu-ZXRIR7fv2T .hl-autocomplete-button{background:#fff}#nav-menu-ZXRIR7fv2T .hl-autocomplete-input-wrapper{color:#000;background-color:#fff}#nav-menu-ZXRIR7fv2T .hl-autocomplete-results{border:1px solid #cacaca;border-top:none;border-radius:16px;border-top-left-radius:0;border-top-right-radius:0;color:var(--white);background-color:var(--transparent)}#nav-menu-ZXRIR7fv2T .hl-autocomplete-input:hover{border-width:1.5px;margin:.5px}#nav-menu-ZXRIR7fv2T .hl-autocomplete-input:focus-within{border-width:2px;margin:0}#col-rUj1LnNf1n>.inner{flex-direction:column;justify-content:center;align-items:inherit;flex-wrap:nowrap}@media screen and (min-width:481px) and (max-width:10000px){.button-dDRV8gIyYx .button-icon-end,.button-dDRV8gIyYx .button-icon-start,.button-dDRV8gIyYx .main-heading-button{font-size:14px;font-weight:undefined}.button-dDRV8gIyYx .button-icon-start{margin-right:5px}.button-dDRV8gIyYx .button-icon-end{margin-left:5px}.button-dDRV8gIyYx .sub-heading-button{font-size:15px;color:var(--white);font-weight:undefined}}@media screen and (min-width:0px) and (max-width:480px){.button-dDRV8gIyYx .button-icon-end,.button-dDRV8gIyYx .button-icon-start,.button-dDRV8gIyYx .main-heading-button{font-size:14px;font-weight:undefined}.button-dDRV8gIyYx .button-icon-start{margin-right:5px}.button-dDRV8gIyYx .button-icon-end{margin-left:5px}.button-dDRV8gIyYx .sub-heading-button{font-size:15px;color:var(--white);font-weight:undefined}}
 /* ---- Section styles with modern blue theme ----- */
:root{--black:#1e293b;--transparent:transparent;--color-lkbjs320:#f8fafc}

/* Modern section animations */
.hl_page-preview--content .section-wAGehRwoGr {
    animation: fadeInUp 0.8s ease-out;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.hl_page-preview--content .section-wAGehRwoGr::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(30, 64, 175, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

.hl_page-preview--content .section-wAGehRwoGr > .inner {
    position: relative;
    z-index: 2;
}.hl_page-preview--content .row-N6rakETuRp,.hl_page-preview--content .section-wAGehRwoGr{padding:0 0 118px;margin-top:0;margin-bottom:0;background-color:var(--color-lkbjs320);border-color:var(--black);border-width:2px;border-style:solid}.hl_page-preview--content .row-N6rakETuRp{padding:0;background-color:var(--transparent);width:100%}.hl_page-preview--content .col-V1zVpPfYjM{width:100%}.hl_page-preview--content .col-V1zVpPfYjM .inner,.hl_page-preview--content .col-eosBjsQX8W .inner{padding:0 0 20px;background-color:var(--transparent);width:100%;border-color:var(--transparent);border-width:1px;border-style:solid;margin-top:0;margin-bottom:0;margin-right:0}.hl_page-preview--content .custom-code-F-9ZM6pPpK{margin:0}.hl_page-preview--content .col-eosBjsQX8W{width:100%}.hl_page-preview--content .custom-code--_R3OxCxtx{margin:0}.hl_page-preview--content .col-DkXLioh8YD,.hl_page-preview--content .row-2tWJqvlq4x{margin:0 auto;box-shadow:none;padding:15px 0 10px;background-color:var(--transparent);border-color:var(--black);border-width:2px;border-style:solid;width:100%}.hl_page-preview--content .col-DkXLioh8YD{padding:10px 5px;margin:0}.hl_page-preview--content .custom-code-G7rpcv-W_V{margin:0}#section-wAGehRwoGr>.inner{max-width:1170px}#col-DkXLioh8YD>.inner,#col-V1zVpPfYjM>.inner,#col-eosBjsQX8W>.inner{flex-direction:column;justify-content:center;align-items:inherit;flex-wrap:nowrap}
 /* ---- Section styles with modern blue theme ----- */
:root{--white:#FFFFFF;--black:#1e293b;--transparent:transparent;--color-lkbhfki1:#1e293b;--color-lkbhik22:#3b82f6;--color-m1tdsdiu:#FFFFFF}

.hl_page-preview--content .section-sfVzbZHQjy{
    padding:20px 0;
    margin-top:0;
    margin-bottom:0;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color:var(--black);
    border-width:2px;
    border-style:solid;
    position: relative;
    overflow: hidden;
    animation: slideInRight 0.8s ease-out 0.2s both;
}

.hl_page-preview--content .section-sfVzbZHQjy::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 70% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 30% 70%, rgba(30, 64, 175, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

.hl_page-preview--content .section-sfVzbZHQjy > .inner {
    position: relative;
    z-index: 2;
}.hl_page-preview--content .col-eMIP6gTw6q,.hl_page-preview--content .row-Vw0H17D-8H{margin:0 auto;box-shadow:none;padding:0;background-color:var(--transparent);border-color:var(--black);border-width:2px;border-style:solid;width:100%}.hl_page-preview--content .col-eMIP6gTw6q{padding:10px 5px;margin:0}.hl_page-preview--content .custom-code-D0vHByKM9n{margin:0}.hl_page-preview--content .button-XOKFNf57PF{margin-top:0;margin-bottom:0;text-align:center}.hl_page-preview--content .cbutton-XOKFNf57PF{font-family:var(--poppins);background:linear-gradient(135deg, #3b82f6, #1e40af);color:var(--color-m1tdsdiu);secondary-color:var(--white);text-decoration:none;padding:14px 40px;font-weight:700;border-color:var(--transparent);border-width:1px;border-style:solid;letter-spacing:0;text-transform:none;text-shadow:0 0 0 transparent;width:auto%;transition:all 0.3s ease;box-shadow:0 4px 15px rgba(59, 130, 246, 0.3);border-radius:50px;position:relative;overflow:hidden}

.hl_page-preview--content .cbutton-XOKFNf57PF:hover{transform:translateY(-2px);box-shadow:0 8px 25px rgba(59, 130, 246, 0.4);background:linear-gradient(135deg, #2563eb, #1d4ed8)}

.hl_page-preview--content .cbutton-XOKFNf57PF::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);transition:left 0.5s;z-index:1}

.hl_page-preview--content .cbutton-XOKFNf57PF:hover::before{left:100%}#section-sfVzbZHQjy>.inner{max-width:1170px}#col-eMIP6gTw6q>.inner{flex-direction:column;justify-content:center;align-items:inherit;flex-wrap:nowrap}@media screen and (min-width:481px) and (max-width:10000px){.button-XOKFNf57PF .button-icon-end,.button-XOKFNf57PF .button-icon-start,.button-XOKFNf57PF .main-heading-button{font-size:14px;font-weight:undefined}.button-XOKFNf57PF .button-icon-start{margin-right:5px}.button-XOKFNf57PF .button-icon-end{margin-left:5px}.button-XOKFNf57PF .sub-heading-button{font-size:15px;color:var(--white);font-weight:undefined}}@media screen and (min-width:0px) and (max-width:480px){.button-XOKFNf57PF .button-icon-end,.button-XOKFNf57PF .button-icon-start,.button-XOKFNf57PF .main-heading-button{font-size:14px;font-weight:undefined}.button-XOKFNf57PF .button-icon-start{margin-right:5px}.button-XOKFNf57PF .button-icon-end{margin-left:5px}.button-XOKFNf57PF .sub-heading-button{font-size:15px;color:var(--white);font-weight:undefined}}
 /* ---- Section styles with modern blue theme ----- */
:root{--white:#FFFFFF;--black:#1e293b;--transparent:transparent;--color-lkbhik22:#3b82f6;--color-lkbjs320:#f8fafc;--color-m1tdsdiu:#FFFFFF}

.hl_page-preview--content .section-ay3GWFejuN{
    padding:20px 0;
    margin-top:0;
    margin-bottom:0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-color:var(--black);
    border-width:2px;
    border-style:solid;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.hl_page-preview--content .section-ay3GWFejuN::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

.hl_page-preview--content .section-ay3GWFejuN > .inner {
    position: relative;
    z-index: 2;
}.hl_page-preview--content .col-4Uk1NVJdvV,.hl_page-preview--content .row-fXS5rW5fqi{margin:0 auto;box-shadow:none;padding:0;background-color:var(--transparent);border-color:var(--black);border-width:2px;border-style:solid;width:100%}.hl_page-preview--content .col-4Uk1NVJdvV{padding:10px 5px;margin:0}.hl_page-preview--content .button-cX3vuxtbMw{margin-top:0;margin-bottom:0;text-align:center}.hl_page-preview--content .cbutton-cX3vuxtbMw{font-family:var(--poppins);background:linear-gradient(135deg, #3b82f6, #1e40af);color:var(--color-m1tdsdiu);secondary-color:var(--white);text-decoration:none;padding:14px 40px;font-weight:700;border-color:var(--transparent);border-width:1px;border-style:solid;letter-spacing:0;text-transform:none;text-shadow:0 0 0 transparent;width:auto%;transition:all 0.3s ease;box-shadow:0 4px 15px rgba(59, 130, 246, 0.3);border-radius:50px;position:relative;overflow:hidden}

.hl_page-preview--content .cbutton-cX3vuxtbMw:hover{transform:translateY(-2px);box-shadow:0 8px 25px rgba(59, 130, 246, 0.4);background:linear-gradient(135deg, #2563eb, #1d4ed8)}

.hl_page-preview--content .cbutton-cX3vuxtbMw::before{content:'';position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);transition:left 0.5s;z-index:1}

.hl_page-preview--content .cbutton-cX3vuxtbMw:hover::before{left:100%}.hl_page-preview--content .custom-code-IrpQK2lftA{margin:0}#section-ay3GWFejuN>.inner{max-width:1170px}#col-4Uk1NVJdvV>.inner{flex-direction:column;justify-content:center;align-items:inherit;flex-wrap:nowrap}@media screen and (min-width:481px) and (max-width:10000px){.button-cX3vuxtbMw .button-icon-end,.button-cX3vuxtbMw .button-icon-start,.button-cX3vuxtbMw .main-heading-button{font-size:14px;font-weight:undefined}.button-cX3vuxtbMw .button-icon-start{margin-right:5px}.button-cX3vuxtbMw .button-icon-end{margin-left:5px}.button-cX3vuxtbMw .sub-heading-button{font-size:15px;color:var(--white);font-weight:undefined}}@media screen and (min-width:0px) and (max-width:480px){.button-cX3vuxtbMw .button-icon-end,.button-cX3vuxtbMw .button-icon-start,.button-cX3vuxtbMw .main-heading-button{font-size:14px;font-weight:undefined}.button-cX3vuxtbMw .button-icon-start{margin-right:5px}.button-cX3vuxtbMw .button-icon-end{margin-left:5px}.button-cX3vuxtbMw .sub-heading-button{font-size:15px;color:var(--white);font-weight:undefined}}
 /* ---- Discovery Call styles ----- */ 
:root{--black:#000000;--transparent:transparent}.hl_page-preview--content .col-3YGUrwgtwd,.hl_page-preview--content .row-zSC_XVvBwG,.hl_page-preview--content .section-ki-126gO-0{box-shadow:none;padding:20px 0;margin:0;background-color:var(--transparent);border-color:var(--black);border-width:2px;border-style:solid}.hl_page-preview--content .col-3YGUrwgtwd,.hl_page-preview--content .row-zSC_XVvBwG{margin:0 auto;padding:15px 0;width:100%}.hl_page-preview--content .col-3YGUrwgtwd{padding:10px 5px;margin:0}.hl_page-preview--content .custom-code-KFlAqIVW_K{margin:0}#section-ki-126gO-0>.inner{max-width:1170px}#col-3YGUrwgtwd>.inner{flex-direction:column;justify-content:center;align-items:inherit;flex-wrap:nowrap}
 /* ---- Footer styles with modern blue theme ----- */
 :root{--white:#FFFFFF;--black:#1e293b;--transparent:transparent;--color-ljn1ecb0:#1e40af;--color-ljn4i7ro:#dbeafe;--color-lkbhfki1:#1e293b}

.hl_page-preview--content .section-kr3hxqv4nG{
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out 0.6s both;
}

.hl_page-preview--content .section-kr3hxqv4nG::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    pointer-events: none;
    z-index: 1;
}

.hl_page-preview--content .section-kr3hxqv4nG > .inner {
    position: relative;
    z-index: 2;
}.hl_page-preview--content .row-Jpvu0Q0cfHn,.hl_page-preview--content .section-kr3hxqv4nG{padding:51px 0 52px;margin-top:0;margin-bottom:0;background-color:var(--color-lkbhfki1);border-color:var(--black);border-width:2px;border-style:solid}.hl_page-preview--content .row-Jpvu0Q0cfHn{padding:0;background-color:var(--transparent);width:100%}.hl_page-preview--content .col-PiXgaEIcg0E{width:21.4%}.hl_page-preview--content .col-PiXgaEIcg0E .inner,.hl_page-preview--content .col-hC6omOqyxcy .inner,.hl_page-preview--content .col-xfeMEp_LXBJ .inner{padding:0;background-color:var(--transparent);width:21.4%;border-color:var(--black);border-width:2px;border-style:solid;margin-top:0;margin-bottom:0}.hl_page-preview--content .button-2MeJJFeA3Dd{margin-top:0;margin-bottom:0;text-align:center}.hl_page-preview--content .cbutton-2MeJJFeA3Dd{font-family:var(--headlinefont);background-color:var(--white);color:var(--color-lkbhfki1);secondary-color:var(--color-ljn1ecb0);text-decoration:none;padding:13px 5px 13px 15px;font-weight:700;border-color:var(--transparent);border-width:1px;border-style:solid;letter-spacing:0;text-transform:none;text-shadow:0 0 0 transparent;width:auto%}.hl_page-preview--content .col-xfeMEp_LXBJ{width:40.3%}.hl_page-preview--content .col-hC6omOqyxcy .inner,.hl_page-preview--content .col-xfeMEp_LXBJ .inner{width:40.3%;margin-bottom:10px}.hl_page-preview--content #paragraph-7jGhmntDzb{margin:0}.hl_page-preview--content .cparagraph-7jGhmntDzb{font-family:var(--contentfont);background-color:var(--transparent);color:var(--white);icon-color:var(--text-color);font-weight:medium;box-shadow:none;padding:0;opacity:1;text-shadow:none;border-color:var(--black);border-width:2px;border-style:solid;line-height:1.3em;letter-spacing:0;text-align:center}.hl_page-preview--content .col-hC6omOqyxcy{width:38.4%}.hl_page-preview--content .col-hC6omOqyxcy .inner{padding:0 5px;width:38.4%;margin-bottom:0}.hl_page-preview--content .image-sH5K7FFn_vn{margin-top:0;margin-bottom:0}.hl_page-preview--content .image-sH5K7FFn_vn .image-container img{box-shadow:undefined;width:240px}.hl_page-preview--content .cimage-sH5K7FFn_vn{padding:0;background-color:var(--transparent);opacity:1;text-align:left}#section-kr3hxqv4nG>.inner{max-width:1170px}#col-PiXgaEIcg0E>.inner{flex-direction:row;justify-content:center;align-items:center;flex-wrap:wrap}.button-2MeJJFeA3Dd .button-icon-start:before{content:"";font-family:"Font Awesome 5 Brands";font-weight:700}@media screen and (min-width:481px) and (max-width:10000px){.button-2MeJJFeA3Dd .button-icon-end,.button-2MeJJFeA3Dd .button-icon-start,.button-2MeJJFeA3Dd .main-heading-button{font-size:20px;font-weight:undefined}.button-2MeJJFeA3Dd .button-icon-start{margin-right:5px}.button-2MeJJFeA3Dd .button-icon-end{margin-left:5px}.button-2MeJJFeA3Dd .sub-heading-button{font-size:15px;color:var(--color-ljn1ecb0);font-weight:undefined}}@media screen and (min-width:0px) and (max-width:480px){.button-2MeJJFeA3Dd .button-icon-end,.button-2MeJJFeA3Dd .button-icon-start,.button-2MeJJFeA3Dd .main-heading-button{font-size:20px;font-weight:undefined}.button-2MeJJFeA3Dd .button-icon-start{margin-right:5px}.button-2MeJJFeA3Dd .button-icon-end{margin-left:5px}.button-2MeJJFeA3Dd .sub-heading-button{font-size:15px;color:var(--color-ljn1ecb0);font-weight:undefined}}#col-xfeMEp_LXBJ>.inner{flex-direction:column;justify-content:flex-start;align-items:inherit;flex-wrap:nowrap}.paragraph-7jGhmntDzb{font-weight:500}.paragraph-7jGhmntDzb em,.paragraph-7jGhmntDzb strong,.paragraph-7jGhmntDzb u{color:var(--text-color)!important}.paragraph-7jGhmntDzb a,.paragraph-7jGhmntDzb a *{color:var(--color-ljn4i7ro)!important;text-decoration:none}.paragraph-7jGhmntDzb a:hover{text-decoration:underline}@media screen and (min-width:0px) and (max-width:480px){.paragraph-7jGhmntDzb h1,.paragraph-7jGhmntDzb h2,.paragraph-7jGhmntDzb h3,.paragraph-7jGhmntDzb h4,.paragraph-7jGhmntDzb h5,.paragraph-7jGhmntDzb h6,.paragraph-7jGhmntDzb ul li,.paragraph-7jGhmntDzb.text-output{font-size:14px!important;font-weight:500}}@media screen and (min-width:481px) and (max-width:10000px){.paragraph-7jGhmntDzb h1,.paragraph-7jGhmntDzb h2,.paragraph-7jGhmntDzb h3,.paragraph-7jGhmntDzb h4,.paragraph-7jGhmntDzb h5,.paragraph-7jGhmntDzb h6,.paragraph-7jGhmntDzb ul li,.paragraph-7jGhmntDzb.text-output{font-size:14px!important;font-weight:500}}.paragraph-7jGhmntDzb.text-output h1:first-child:before,.paragraph-7jGhmntDzb.text-output h2:first-child:before,.paragraph-7jGhmntDzb.text-output h3:first-child:before,.paragraph-7jGhmntDzb.text-output h4:first-child:before,.paragraph-7jGhmntDzb.text-output h5:first-child:before,.paragraph-7jGhmntDzb.text-output h6:first-child:before,.paragraph-7jGhmntDzb.text-output p:first-child:before{color:var(--text-color);content:'\';
    font-family: '';margin-right:5px;font-weight:700}#col-hC6omOqyxcy>.inner{flex-direction:column;justify-content:center;align-items:inherit;flex-wrap:nowrap}

</style>
<style type="text/css">
  @media (max-width: 480px) {
        .bg-section-m0AmIUbB7 {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_480/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3);
    opacity: 1

        }
    }
    
    @media (min-width: 481px) and (max-width: 1024px) {
        .bg-section-m0AmIUbB7 {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_768/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3);
    opacity: 1

        }
    }

    @media (min-width: 1025px) {
        .bg-section-m0AmIUbB7 {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3);
        opacity: 1

        }
    }
  </style>
<style type="text/css">
  @media (max-width: 480px) {
        .bg-section-sfVzbZHQjy {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_480/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg3.png?alt=media&token=9c1f5561-e962-4487-bbf4-e72a4b52c54b);
    opacity: 1

        }
    }
    
    @media (min-width: 481px) and (max-width: 1024px) {
        .bg-section-sfVzbZHQjy {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_768/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg3.png?alt=media&token=9c1f5561-e962-4487-bbf4-e72a4b52c54b);
    opacity: 1

        }
    }

    @media (min-width: 1025px) {
        .bg-section-sfVzbZHQjy {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg3.png?alt=media&token=9c1f5561-e962-4487-bbf4-e72a4b52c54b);
        opacity: 1

        }
    }
  </style>
<style type="text/css">
  @media (max-width: 480px) {
        .bg-section-ki-126gO-0 {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_480/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3);
    opacity: 1

        }
    }
    
    @media (min-width: 481px) and (max-width: 1024px) {
        .bg-section-ki-126gO-0 {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_768/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3);
    opacity: 1

        }
    }

    @media (min-width: 1025px) {
        .bg-section-ki-126gO-0 {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3);
        opacity: 1

        }
    }
  </style>
<style type="text/css">
  @media (max-width: 480px) {
        .bg-section-kr3hxqv4nG {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_480/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3);
    opacity: 1

        }
    }
    
    @media (min-width: 481px) and (max-width: 1024px) {
        .bg-section-kr3hxqv4nG {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_768/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3);
    opacity: 1

        }
    }

    @media (min-width: 1025px) {
        .bg-section-kr3hxqv4nG {
          background: url(https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3);
        opacity: 1

        }
    }
  </style>
<style>.flex{display:flex}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.flex-grow{flex-grow:9999}.align-center{align-items:center}.h-full{height:100%}.max-w-400{max-width:400px}.text-right{text-align:right}.d-grid{display:grid}.px-0{padding-left:0!important;padding-right:0!important}.relative{position:relative}.gap-2{gap:.625rem}.mt-20{margin-top:20px}.mt-8{margin-top:32px}.ml-3{margin-left:.75rem}.mr-10{margin-right:10px}.mt-4{margin-top:1rem}.mb-4{margin-bottom:1rem}.w-100{width:100%}.h-100{height:100%}.w-50{width:50%}.w-25{width:25%}.mw-100{max-width:100%}.noBorder{border:none!important}.iti__flag{background-image:url(https://stcdn.leadconnectorhq.com/intl-tel-input/17.0.12/img/flags.png)}.pointer{cursor:pointer}@media (-webkit-min-device-pixel-ratio:2),(min-resolution:192dpi){.iti__flag{background-image:url(https://stcdn.leadconnectorhq.com/intl-tel-input/17.0.12/img/<EMAIL>)}}.iti__country{display:flex;justify-content:space-between}@media (min-width:768px){.hl_wrapper.nav-shrink .hl_wrapper--inner.page-creator,body{padding-top:0}.hl_page-creator--menu{left:0;top:0;z-index:10}.hl_wrapper{padding-left:0}}@media (min-width:1200px){.hl_wrapper.nav-shrink{padding-left:0!important}}html body .hl_wrapper{height:100vh;overflow:hidden}body{margin:0;-webkit-font-smoothing:antialiased}img{border-style:none;vertical-align:middle}.bg-fixed{z-index:-1}.progress-outer{background-color:#f5f5f5;border-radius:inherit;box-shadow:inset 0 1px 2px rgba(0,0,0,.1);font-size:14px;height:35px;line-height:36px;overflow:hidden;padding-bottom:0;padding-top:0;width:100%}.progress-inner{box-shadow:inset 0 -1px 0 rgba(0,0,0,.15);color:#fff;float:left;font-size:14px;height:100%;padding-left:10px;padding-right:10px;transition:width .6s ease;width:0}.progress0{width:0}.progress10{width:10%}.progress20{width:20%}.progress30{width:30%}.progress40{width:40%}.progress50{width:50%}.progress60{width:60%}.progress70{width:70%}.progress80{width:80%}.progress90{width:90%}.progress100{width:100%}.progressbarOffsetWhite{background:#f5f5f5}.progressbarOffsetTransparentWhite{background-color:hsla(0,0%,100%,.5)}.progressbarOffsetBlack{background:#333}.progressbarOffsetTransparentBlack{background-color:hsla(0,0%,49%,.5)}.text-white{color:#fff}.text-bold{font-weight:700}.text-italic{font-style:italic}.text-bold-italic{font-style:italic;font-weight:700}.progressbarSmall{font-size:14px;height:35px;line-height:36px}.progressbarMedium{font-size:19px;height:45px;line-height:45px}.progressbarLarge{font-size:21px;height:65px;line-height:65px}.recaptcha-container{margin-bottom:1em}.recaptcha-container p{color:red;margin-top:1em}.button-recaptcha-container div:first-child{height:auto!important;width:100%!important}.card-el-error-msg{align-items:center;color:#e25950;display:flex;font-size:13px;justify-content:flex-start;padding:10px 0;text-align:center}.card-el-error-msg svg{color:#f87171;margin-right:2px}.hl-faq-child-heading{border:none;cursor:pointer;justify-content:space-between;outline:none;padding:15px;width:100%}.hl-faq-child-head,.hl-faq-child-heading{align-items:center;display:flex}.v-enter-active,.v-leave-active{transition:opacity .2s ease-out}.v-enter-from,.v-leave-to{opacity:0}.faq-separated-child{margin-bottom:10px}.hl-faq-child-panel img{border-radius:15px;cursor:pointer}.hl-faq-child-heading-icon.left{margin-right:1em}.expand-collapse-all-button{background-color:transparent;border:1px solid #d1d5db;border-radius:15px;color:#3b82f6;cursor:pointer;font-size:12px;font-weight:400;line-height:16px;margin:1em 0;padding:5px 15px}.hl-faq-child-panel{transition:padding .2s ease}.v-spinner .v-moon1{position:relative}.v-spinner .v-moon1,.v-spinner .v-moon2{animation:v-moonStretchDelay .6s linear 0s infinite;animation-fill-mode:forwards}.v-spinner .v-moon2{opacity:.8;position:absolute}.v-spinner .v-moon3{opacity:.1}@keyframes v-moonStretchDelay{to{transform:rotate(1turn)}}.generic-error-message{color:red;font-weight:500;margin-top:.5rem;text-align:center}#faq-overlay{background:var(--overlay);height:100vh;opacity:.8;width:100vw}#faq-overlay,#faq-popup{position:fixed;z-index:1000}#faq-popup{background:#fff;height:auto;left:50%;margin-left:-250px;margin-top:-250px;top:50%;width:500px}#popupclose{cursor:pointer;float:right;padding:10px}.popupcontent{height:auto!important;width:100%!important}#button{cursor:pointer}.dark{background-color:#000}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.spotlight{background:linear-gradient(45deg,#00dc82,#36e4da 50%,#0047e1);bottom:-30vh;filter:blur(20vh);height:40vh}.z-10{z-index:10}.right-0{right:0}.left-0{left:0}.fixed{position:fixed}.text-black{--tw-text-opacity:1;color:rgba(0,0,0,var(--tw-text-opacity))}.overflow-hidden{overflow:hidden}.min-h-screen{min-height:100vh}.font-sans{font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji}.place-content-center{place-content:center}.grid{display:grid}.z-20{z-index:20}.max-w-520px{max-width:520px}.mb-8{margin-bottom:2rem}.text-8xl{font-size:6rem;line-height:1}.font-medium{font-weight:500}.mb-16{margin-bottom:4rem}.leading-tight{line-height:1.25}.text-xl{font-size:1.25rem;line-height:1.75rem}.font-light{font-weight:300}@media (min-width:640px){.sm-text-10xl{font-size:10rem;line-height:1}.sm-text-4xl{font-size:2.25rem;line-height:2.5rem}.sm-px-0{padding-left:0;padding-right:0}}.full-center{background-position:50%!important;background-repeat:repeat!important;background-size:cover!important}.fill-width{background-size:100% auto!important}.fill-width,.fill-width-height{background-repeat:no-repeat!important}.fill-width-height{background-size:100% 100%!important}.no-repeat{background-repeat:no-repeat!important}.repeat-x{background-repeat:repeat-x!important}.repeat-y{background-repeat:repeat-y!important}.repeat-x-fix-top{background-position:top!important;background-repeat:repeat-x!important}.repeat-x-fix-bottom{background-position:bottom!important;background-repeat:repeat-x!important}#overlay{bottom:0;height:100%;left:0;opacity:0;overflow-y:scroll;position:fixed;right:0;top:0;transition:opacity .3s ease;width:100%;z-index:999;-webkit-overflow-scrolling:touch}#overlay.show{opacity:1}.popup-body{background-color:#fff;height:auto;left:50%;min-height:180px;position:absolute;top:10%;transform:translate(-50%,-100vh);transition:transform .25s ease-in-out;z-index:20}.popup-body.show{transform:translate(-50%)}.closeLPModal{cursor:pointer;position:absolute;right:-10px;top:-10px;z-index:21}.settingsPModal{font-size:18px;left:40%;padding:10px;position:absolute;top:-40px;width:32px}.c-section>.inner{display:flex;flex-direction:column;justify-content:center;margin:auto;z-index:2}.c-row>.inner{display:flex;width:100%}.c-column>.inner{display:flex;flex-direction:column;height:100%;justify-content:inherit;width:100%!important}.c-wrapper{position:relative}.previewer{--vw:100vh/100;height:calc(100vh - 170px);margin:auto;overflow:scroll;overflow-x:hidden;overflow-y:scroll;width:100%}.c-element{position:relative}.c-column{flex:1}.c-column,.c-row{position:relative}p+p{margin-top:auto}.hl_page-creator--row.active{border-color:#188bf6}.flip-list-move{transition:transform .5s}.page-wrapper .sortable-ghost:before{background:#188bf6!important;border-radius:4px;content:"";height:4px;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:100%}.page-wrapper .sortable-ghost{border:none!important;position:relative}.active-drop-area:before{color:grey;content:"";font-size:12px;left:50%;pointer-events:none;position:absolute;top:50%;transform:translate(-50%,-50%)}.active-drop-area{border:1px dashed grey}.active-drop-area.is-empty{min-height:60px}.empty-component{align-items:center;border:1px dashed #d7dde9;display:flex;height:100%;justify-content:center;left:0;position:absolute;top:0;width:100%;z-index:2}.empty-component,.empty-component-min-height{min-height:100px;pointer-events:none}.dividerContainer{width:100%}.items-center{align-items:center}.font-semibold{font-weight:600}.text-2xl{font-size:1.5rem}.text-sm{font-size:.875rem}.w-full{width:100%}.mt-2{margin-top:.5rem}.justify-between{justify-content:space-between}.text-lg{font-size:1.125rem}.font-base{font-weight:400}.justify-end{justify-content:flex-end}.justify-center{justify-content:center!important}.text-center{text-align:center}.centered{align-items:center;display:flex;height:100%;justify-content:center;width:100%}.mx-auto{margin:0 auto}</style>
<style>@media only screen and (max-width:767px){.c-row>.inner{flex-direction:column}.desktop-only{display:none}.c-column,.c-row{width:100%!important}.c-column,.c-column>.inner,.c-row>.inner,.c-section,.c-section>.inner{padding-left:0!important;padding-right:0!important}.c-column,.c-column>.inner,.c-row{margin-left:0!important;margin-right:0!important}.c-row{padding-left:10px!important;padding-right:10px!important}}@media only screen and (min-width:768px){.mobile-only{display:none}}.c-button button{outline:none;position:relative}.fa,.fab,.fal,.far,.fas{-webkit-font-smoothing:antialiased;display:inline-block;font-style:normal;font-variant:normal;line-height:1;text-rendering:auto}.c-column{flex:1 1 auto!important}.c-column>.inner.horizontal>div{flex:1}.c-row>.inner{display:flex;width:100%}.bgCover.bg-fixed:before{background-attachment:fixed!important;background-position:50%!important;background-repeat:repeat!important;background-size:cover!important;-webkit-background-size:cover!important}@supports (-webkit-touch-callout:inherit){.bgCover.bg-fixed:before{background-attachment:scroll!important}}.bgCover100.bg-fixed:before{background-size:100% auto!important;-webkit-background-size:100% auto!important}.bgCover100.bg-fixed:before,.bgNoRepeat.bg-fixed:before{background-repeat:no-repeat!important}.bgRepeatX.bg-fixed:before{background-repeat:repeat-x!important}.bgRepeatY.bg-fixed:before{background-repeat:repeat-y!important}.bgRepeatXTop.bg-fixed:before{background-position:top!important;background-repeat:repeat-x!important}.bgRepeatXBottom.bg-fixed:before{background-position:bottom!important;background-repeat:repeat-x!important}.bgCover{background-attachment:fixed!important;background-position:50%!important;background-repeat:repeat!important;background-size:cover!important;-webkit-background-size:cover!important}@supports (-webkit-touch-callout:inherit){.bgCover{background-attachment:scroll!important}}.bgCover100{background-size:100% auto!important;-webkit-background-size:100% auto!important}.bgCover100,.bgNoRepeat{background-repeat:no-repeat!important}.bgRepeatX{background-repeat:repeat-x!important}.bgRepeatY{background-repeat:repeat-y!important}.bgRepeatXTop{background-position:top!important}.bgRepeatXBottom,.bgRepeatXTop{background-repeat:repeat-x!important}.bgRepeatXBottom{background-position:bottom!important}.cornersTop{border-bottom-left-radius:0!important;border-bottom-right-radius:0!important}.cornersBottom{border-top-left-radius:0!important;border-top-right-radius:0!important}.radius0{border-radius:0}.radius1{border-radius:1px}.radius2{border-radius:2px}.radius3{border-radius:3px}.radius4{border-radius:4px}.radius5{border-radius:5px}.radius10{border-radius:10px}.radius15{border-radius:15px}.radius20{border-radius:20px}.radius25{border-radius:25px}.radius50{border-radius:50px}.radius75{border-radius:75px}.radius100{border-radius:100px}.radius125{border-radius:125px}.radius150{border-radius:150px}.borderTopBottom{border-bottom-color:rgba(0,0,0,.7);border-left:none!important;border-right:none!important;border-top-color:rgba(0,0,0,.7)}.borderTop{border-bottom:none!important;border-top-color:rgba(0,0,0,.7)}.borderBottom,.borderTop{border-left:none!important;border-right:none!important}.borderBottom{border-bottom-color:rgba(0,0,0,.7);border-top:none!important}.borderFull{border-color:rgba(0,0,0,.7)}@keyframes rocking{0%{transform:rotate(0deg)}25%{transform:rotate(0deg)}50%{transform:rotate(2deg)}75%{transform:rotate(-2deg)}to{transform:rotate(0deg)}}.buttonRocking{animation:rocking 2s infinite;animation-timing-function:ease-out;transition:.2s}.buttonPulseGlow{animation:pulseGlow 2s infinite;animation-timing-function:ease-in-out}@keyframes pulseGlow{0%{box-shadow:0 0 0 0 hsla(0,0%,100%,0)}25%{box-shadow:0 0 2.5px 1px hsla(0,0%,100%,.25)}50%{box-shadow:0 0 5px 2px hsla(0,0%,100%,.5)}85%{box-shadow:0 0 5px 5px hsla(0,0%,100%,0)}to{box-shadow:0 0 0 0 hsla(0,0%,100%,0)}}.buttonBounce{animation:bounce 1.5s infinite;animation-timing-function:ease-in;transition:.2s}@keyframes bounce{15%{box-shadow:0 0 0 0 transparent;transform:translateY(0)}35%{box-shadow:0 8px 5px -5px rgba(0,0,0,.25);transform:translateY(-35%)}45%{box-shadow:0 0 0 0 transparent;transform:translateY(0)}55%{box-shadow:0 5px 4px -4px rgba(0,0,0,.25);transform:translateY(-20%)}70%{box-shadow:0 0 0 0 transparent;transform:translateY(0)}80%{box-shadow:0 4px 3px -3px rgba(0,0,0,.25);transform:translateY(-10%)}90%{box-shadow:0 0 0 0 transparent;transform:translateY(0)}95%{box-shadow:0 2px 3px -3px rgba(0,0,0,.25);transform:translateY(-2%)}99%{box-shadow:0 0 0 0 transparent;transform:translateY(0)}to{box-shadow:0 0 0 0 transparent;transform:translateY(0)}}@keyframes elevate{0%{box-shadow:0 0 0 0 transparent;transform:translateY(0)}to{box-shadow:0 8px 5px -5px rgba(0,0,0,.25);transform:translateY(-10px)}}.buttonElevate:hover{animation:elevate .2s forwards}.buttonElevate{box-shadow:0 0 0 0 transparent;transition:.2s}.buttonWobble{transition:.3s}.buttonWobble:hover{animation:wobble .5s 1;animation-timing-function:ease-in-out}@keyframes wobble{0%{transform:skewX(0deg)}25%{transform:skewX(10deg)}50%{transform:skewX(0deg)}75%{transform:skewX(-10deg)}to{transform:skewX(0deg)}}.image-container img{max-width:100%;vertical-align:middle}.sub-text ::-moz-placeholder{color:#000;opacity:1}.sub-text ::placeholder{color:#000;opacity:1}.image-container{height:100%;width:100%}.shadow5inner{box-shadow:inset 0 1px 3px rgba(0,0,0,.05)}.shadow10inner{box-shadow:inset 0 1px 5px rgba(0,0,0,.1)}.shadow20inner{box-shadow:inset 0 1px 5px rgba(0,0,0,.2)}.shadow30inner{box-shadow:inset 0 2px 5px 2px rgba(0,0,0,.3)}.shadow40inner{box-shadow:inset 0 2px 5px 2px rgba(0,0,0,.4)}.shadow5{box-shadow:0 1px 3px rgba(0,0,0,.05)}.shadow10{box-shadow:0 1px 5px rgba(0,0,0,.1)}.shadow20{box-shadow:0 1px 5px rgba(0,0,0,.2)}.shadow30{box-shadow:0 2px 5px 2px rgba(0,0,0,.3)}.shadow40{box-shadow:0 2px 5px 2px rgba(0,0,0,.4)}.sub-heading-button{color:#fff;font-weight:400;line-height:normal;opacity:.8;text-align:center}.wideSection{max-width:1120px}.midWideSection,.wideSection{margin-left:auto!important;margin-right:auto!important}.midWideSection{max-width:960px}.midSection{margin-left:auto!important;margin-right:auto!important;max-width:720px}.c-section>.inner{margin-left:auto;margin-right:auto;max-width:1170px;width:100%}.c-column{padding-left:15px;padding-right:15px}.feature-img-circle img,.img-circle,.img-circle img{border-radius:50%!important}.feature-img-round-corners img,.img-round-corners,.img-round-corners img{border-radius:5px}.feature-image-dark-border img,.image-dark-border{border:3px solid rgba(0,0,0,.7)}.feature-image-white-border img,.image-white-border{border:3px solid #fff}.img-grey,.img-grey img{filter:grayscale(100%);filter:gray;-webkit-transition:all .6s ease}.button-shadow1{box-shadow:0 1px 5px rgba(0,0,0,.2)}.button-shadow2{box-shadow:0 1px 5px rgba(0,0,0,.4)}.button-shadow3{box-shadow:0 1px 5px rgba(0,0,0,.7)}.button-shadow4{box-shadow:0 8px 1px rgba(0,0,0,.1)}.button-shadow5{box-shadow:0 0 25px rgba(0,0,0,.2),0 0 15px rgba(0,0,0,.2),0 0 3px rgba(0,0,0,.4)}.button-shadow6{box-shadow:0 0 25px rgba(0,0,0,.4),0 0 15px hsla(0,0%,100%,.2),0 0 3px hsla(0,0%,100%,.4)}.button-shadow-sharp1{box-shadow:inset 0 1px 0 hsla(0,0%,100%,.2)}.button-shadow-sharp2{box-shadow:inset 0 0 0 1px hsla(0,0%,100%,.2)}.button-shadow-sharp3{box-shadow:inset 0 0 0 2px hsla(0,0%,100%,.2)}.button-shadow-highlight{box-shadow:none}.button-shadow-highlight:hover{box-shadow:inset 0 0 0 0 hsla(0,0%,100%,.22),inset 0 233px 233px 0 hsla(0,0%,100%,.12)}.button-flat-line{background-color:transparent!important;border-width:2px}.button-vp-5{padding-bottom:5px!important;padding-top:5px!important}.button-vp-10{padding-bottom:10px!important;padding-top:10px!important}.button-vp-15{padding-bottom:15px!important;padding-top:15px!important}.button-vp-20{padding-bottom:20px!important;padding-top:20px!important}.button-vp-25{padding-bottom:25px!important;padding-top:25px!important}.button-vp-30{padding-bottom:30px!important;padding-top:30px!important}.button-vp-40{padding-bottom:40px!important;padding-top:40px!important}.button-vp-0{padding-bottom:0!important;padding-top:0!important}.button-hp-5{padding-left:5px!important;padding-right:5px!important}.button-hp-10{padding-left:10px!important;padding-right:10px!important}.button-hp-15{padding-left:15px!important;padding-right:15px!important}.button-hp-20{padding-left:20px!important;padding-right:20px!important}.button-hp-25{padding-left:25px!important;padding-right:25px!important}.button-hp-30{padding-left:30px!important;padding-right:30px!important}.button-hp-40{padding-left:40px!important;padding-right:40px!important}.button-hp-0{padding-left:0!important;padding-right:0!important}.vs__dropdown-toggle{background:#f3f8fb!important;border:none!important;height:43px!important}.row-align-center{margin:0 auto}.row-align-left{margin:0 auto;margin-left:0!important}.row-align-right{margin:0 auto;margin-right:0!important}button,input,optgroup,select,textarea{border-radius:unset;font-family:unset;font-size:unset;line-height:unset;margin:unset;text-transform:unset}body{font-weight:unset!important;line-height:unset!important;-moz-osx-font-smoothing:grayscale;word-wrap:break-word}*,:after,:before{box-sizing:border-box}.main-heading-group>div{display:inline-block}.c-button span.main-heading-group,.c-button span.sub-heading-group{display:block}.time-grid-3{grid-template-columns:repeat(3,100px)}.time-grid-3,.time-grid-4{display:grid;text-align:center}.time-grid-4{grid-template-columns:repeat(4,100px)}@media screen and (max-width:767px){.time-grid-3{grid-template-columns:repeat(3,80px)}.time-grid-4{grid-template-columns:repeat(4,70px)}}.time-grid .timer-box{display:grid;font-size:15px;grid-template-columns:1fr;text-align:center}.timer-box .label{font-weight:300}.c-button button{cursor:pointer}.c-button>a{text-decoration:none}.c-button>a,.c-button>a span{display:inline-block}.nav-menu-wrapper{display:flex;justify-content:space-between}.nav-menu-wrapper.default{flex-direction:row}.nav-menu-wrapper.reverse{flex-direction:row-reverse}.nav-menu-wrapper .branding{align-items:center;display:flex}.nav-menu-wrapper.default .branding{flex-direction:row}.nav-menu-wrapper.reverse .branding{flex-direction:row-reverse}.nav-menu-wrapper.default .branding .logo,.nav-menu-wrapper.reverse .branding .title{margin-right:18px}.nav-menu-wrapper .branding .title{align-items:center;display:flex;min-height:50px;min-width:50px}.nav-menu{align-items:center;display:flex;flex-wrap:wrap;list-style:none;margin:0;padding:0}.nav-menu a{text-decoration:none}.dropdown{display:inline-block;position:relative}.dropdown .dropdown-menu{border:none;box-shadow:0 8px 16px 5px rgba(0,0,0,.1)}.dropdown-menu{background-clip:padding-box;border:1px solid rgba(0,0,0,.15);border-radius:.3125rem;color:#607179;display:none;float:left;font-size:1rem;left:0;list-style:none;margin:.125rem 0 0;min-width:10rem;padding:.5rem 0;position:absolute;text-align:left;top:100%;z-index:1000}.nav-menu .nav-menu-item.dropdown:hover>.dropdown-menu{display:block}.nav-menu .dropdown-menu{display:none;list-style:none;margin:0;padding:0}.nav-menu-mobile{display:none}.nav-menu-mobile i{cursor:pointer;font-size:24px}#nav-menu-popup{background:var(--overlay);bottom:0;display:none;height:100%;left:0;opacity:0;position:fixed;right:0;top:0;transition:opacity .3s ease;width:100%;z-index:100}#nav-menu-popup.show{opacity:1}#nav-menu-popup .nav-menu-body{background-color:#fff;height:100%;left:0;overflow:auto;padding:45px;position:absolute;top:0;width:100%}#nav-menu-popup .nav-menu-body .close-menu{cursor:pointer;left:20px;position:absolute;top:20px;z-index:100}#nav-menu-popup .nav-menu-body .close-menu:before{content:"\f00d"}#nav-menu-popup .nav-menu{align-items:center;display:flex;flex-direction:column;list-style:none;margin:0;padding:0}#nav-menu-popup .nav-menu .nav-menu-item{list-style:none;text-align:left;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:100%}#nav-menu-popup .nav-menu .nav-menu-item .nav-menu-item-content{display:flex;position:relative}#nav-menu-popup .nav-menu-item .nav-menu-item-title{flex-grow:1;margin:0 1rem;max-width:calc(100% - 2rem)}#nav-menu-popup .nav-menu .nav-menu-item .nav-menu-item-content .nav-menu-item-toggle{cursor:pointer;font-size:24px;position:absolute;right:0;top:calc(50% - 12px)}#nav-menu-popup .nav-menu .nav-menu-item .nav-menu-item-content .nav-menu-item-toggle i{font-size:24px;transition:transform .2s ease}#nav-menu-popup .nav-menu .nav-menu-item .nav-menu-item-content .nav-menu-item-toggle i:before{content:"\f107"}#nav-menu-popup .nav-menu .nav-menu-item.active .nav-menu-item-content .nav-menu-item-toggle i{transform:rotate(-180deg)}#nav-menu-popup .nav-menu .nav-menu-item .nav-dropdown-menu{display:none;max-height:0;opacity:0;overflow:auto;padding:0;transition:all .3s ease-in-out;visibility:hidden}#nav-menu-popup .nav-menu .nav-menu-item.active .nav-dropdown-menu{display:block;max-height:600px;opacity:1;visibility:visible}.form-error{border:2px solid var(--red);border-radius:8px;cursor:pointer;font-size:20px;margin-bottom:10px;padding:6px 12px;text-align:center}.form-error,.form-error i{color:var(--red)}.c-bullet-list ul li{line-height:inherit}.c-bullet-list ul li.ql-indent-1{padding-left:4.5em}.c-bullet-list ul li.ql-indent-2{padding-left:7.5em}.c-bullet-list ul li.ql-indent-3{padding-left:10.5em}.c-bullet-list ul li.ql-indent-4{padding-left:13.5em}.c-bullet-list ul li.ql-indent-5{padding-left:16.5em}.c-bullet-list ul li.ql-indent-6{padding-left:19.5em}.c-bullet-list ul li.ql-indent-7{padding-left:22.5em}.c-bullet-list ul li.ql-indent-8{padding-left:25.5em}.text-output ol,.text-output ul{list-style-type:none;margin:0;padding:0}.text-output ol li{list-style-type:decimal}.text-output ul li{padding-left:1.5em}.text-output ul li:before{display:inline-block;font-weight:700;margin-left:-1.5em;margin-right:.3em;text-align:right;white-space:nowrap;width:1.2em}.svg-component svg{max-height:100%;max-width:100%}.border1{border-bottom:3px solid rgba(0,0,0,.2)!important}.border2{border:2px solid rgba(0,0,0,.55)}.border3{border:solid rgba(0,0,0,.15);border-width:1px 1px 2px;padding:5px}.border4{border:solid rgba(0,0,0,.35);border-width:1px 1px 2px;padding:1px!important}.shadow1{box-shadow:0 10px 6px -6px rgba(0,0,0,.15)}.shadow2{box-shadow:0 4px 3px rgba(0,0,0,.15),0 0 2px rgba(0,0,0,.15)}.shadow3{box-shadow:0 10px 6px -6px #999}.shadow4{box-shadow:3px 3px 15px #212121a8}.shadow6{box-shadow:0 10px 1px #ddd,0 10px 20px #ccc}.background{background-color:unset!important}@keyframes progress-bar-animation{to{background-position:0 -3000px}}@keyframes gradient{0%{background-position:0 50%}50%{background-position:100% 50%}to{background-position:0 50%}}h1,h2,h3,h4,h5,h6{font-weight:500;margin:unset}p{margin:unset}.c-bullet-list,.c-heading,.c-image-feature,.c-paragraph,.c-sub-heading{word-break:break-word}p:empty:after{content:"\00A0"}.w-3-4{width:75%}.w-1-2{width:50%}.tabs-container{display:flex}@media screen and (max-width:767px){.nav-menu{display:none}.nav-menu-mobile{align-items:center;display:flex}.popup-body,.popup-body-lead-video{top:10px!important;width:calc(100% - 10px)!important}#faq-popup{left:5px!important;margin-left:0!important;width:98%!important}.video-container{width:100%!important}.autoplay .vjs-big-play-button{display:none!important}.autoplay:hover .vjs-control-bar{display:flex!important}}</style>
<style>@font-face{font-display:block;font-family:Font Awesome\ 5 Free;font-style:normal;font-weight:400;src:url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-regular-400.eot);src:url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-regular-400.eot?#iefix) format("embedded-opentype"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-regular-400.woff2) format("woff2"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-regular-400.woff) format("woff"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-regular-400.ttf) format("truetype"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-regular-400.svg#fontawesome) format("svg")}.far{font-weight:400}@font-face{font-display:block;font-family:Font Awesome\ 5 Free;font-style:normal;font-weight:900;src:url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-solid-900.eot);src:url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-solid-900.eot?#iefix) format("embedded-opentype"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-solid-900.woff2) format("woff2"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-solid-900.woff) format("woff"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-solid-900.ttf) format("truetype"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-solid-900.svg#fontawesome) format("svg")}.fa,.far,.fas{font-family:Font Awesome\ 5 Free}.fa,.fas{font-weight:900}@font-face{font-display:block;font-family:Font Awesome\ 5 Brands;font-style:normal;font-weight:400;src:url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-brands-400.eot);src:url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-brands-400.eot?#iefix) format("embedded-opentype"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-brands-400.woff2) format("woff2"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-brands-400.woff) format("woff"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-brands-400.ttf) format("truetype"),url(https://stcdn.leadconnectorhq.com/funnel/fontawesome/webfonts/fa-brands-400.svg#fontawesome) format("svg")}.fab{font-family:Font Awesome\ 5 Brands;font-weight:400}</style>
<style>:root{--animate-duration:1s;--animate-delay:1s;--animate-repeat:1}.animate__animated{animation-duration:1s;animation-duration:var(--animate-duration);animation-fill-mode:both}@media (prefers-reduced-motion:reduce),print{.animate__animated{animation-duration:1ms!important;animation-iteration-count:1!important;transition-duration:1ms!important}.animate__animated[class*=Out]{opacity:0}}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}.animate__fadeIn{animation-name:fadeIn}@keyframes fadeInUp{0%{opacity:0;transform:translate3d(0,100%,0)}to{opacity:1;transform:translateZ(0)}}.animate__fadeInUp{animation-name:fadeInUp}@keyframes fadeInDown{0%{opacity:0;transform:translate3d(0,-100%,0)}to{opacity:1;transform:translateZ(0)}}.animate__fadeInDown{animation-name:fadeInDown}@keyframes fadeInLeft{0%{opacity:0;transform:translate3d(-100%,0,0)}to{opacity:1;transform:translateZ(0)}}.animate__fadeInLeft{animation-name:fadeInLeft}@keyframes fadeInRight{0%{opacity:0;transform:translate3d(100%,0,0)}to{opacity:1;transform:translateZ(0)}}.animate__fadeInRight{animation-name:fadeInRight}@keyframes slideInUp{0%{transform:translate3d(0,100%,0);visibility:visible}to{transform:translateZ(0)}}.animate__slideInUp{animation-name:slideInUp}@keyframes slideInDown{0%{transform:translate3d(0,-100%,0);visibility:visible}to{transform:translateZ(0)}}.animate__slideInDown{animation-name:slideInDown}@keyframes slideInLeft{0%{transform:translate3d(-100%,0,0);visibility:visible}to{transform:translateZ(0)}}.animate__slideInLeft{animation-name:slideInLeft}@keyframes slideInRight{0%{transform:translate3d(100%,0,0);visibility:visible}to{transform:translateZ(0)}}.animate__slideInRight{animation-name:slideInRight}@keyframes bounceIn{0%,20%,40%,60%,80%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:scale3d(.3,.3,.3)}20%{transform:scale3d(1.1,1.1,1.1)}40%{transform:scale3d(.9,.9,.9)}60%{opacity:1;transform:scale3d(1.03,1.03,1.03)}80%{transform:scale3d(.97,.97,.97)}to{opacity:1;transform:scaleX(1)}}.animate__bounceIn{animation-duration:.75s;animation-duration:calc(var(--animate-duration)*.75);animation-name:bounceIn}@keyframes bounceInUp{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,3000px,0) scaleY(5)}60%{opacity:1;transform:translate3d(0,-20px,0) scaleY(.9)}75%{transform:translate3d(0,10px,0) scaleY(.95)}90%{transform:translate3d(0,-5px,0) scaleY(.985)}to{transform:translateZ(0)}}.animate__bounceInUp{animation-name:bounceInUp}@keyframes bounceInDown{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(0,-3000px,0) scaleY(3)}60%{opacity:1;transform:translate3d(0,25px,0) scaleY(.9)}75%{transform:translate3d(0,-10px,0) scaleY(.95)}90%{transform:translate3d(0,5px,0) scaleY(.985)}to{transform:translateZ(0)}}.animate__bounceInDown{animation-name:bounceInDown}@keyframes bounceInLeft{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(-3000px,0,0) scaleX(3)}60%{opacity:1;transform:translate3d(25px,0,0) scaleX(1)}75%{transform:translate3d(-10px,0,0) scaleX(.98)}90%{transform:translate3d(5px,0,0) scaleX(.995)}to{transform:translateZ(0)}}.animate__bounceInLeft{animation-name:bounceInLeft}@keyframes bounceInRight{0%,60%,75%,90%,to{animation-timing-function:cubic-bezier(.215,.61,.355,1)}0%{opacity:0;transform:translate3d(3000px,0,0) scaleX(3)}60%{opacity:1;transform:translate3d(-25px,0,0) scaleX(1)}75%{transform:translate3d(10px,0,0) scaleX(.98)}90%{transform:translate3d(-5px,0,0) scaleX(.995)}to{transform:translateZ(0)}}.animate__bounceInRight{animation-name:bounceInRight}@keyframes flip{0%{animation-timing-function:ease-out;transform:perspective(400px) scaleX(1) translateZ(0) rotateY(-1turn)}40%{animation-timing-function:ease-out;transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-190deg)}50%{animation-timing-function:ease-in;transform:perspective(400px) scaleX(1) translateZ(150px) rotateY(-170deg)}80%{animation-timing-function:ease-in;transform:perspective(400px) scale3d(.95,.95,.95) translateZ(0) rotateY(0deg)}to{animation-timing-function:ease-in;transform:perspective(400px) scaleX(1) translateZ(0) rotateY(0deg)}}.animate__animated.animate__flip{animation-name:flip;backface-visibility:visible}@keyframes flipInX{0%{animation-timing-function:ease-in;opacity:0;transform:perspective(400px) rotateX(90deg)}40%{animation-timing-function:ease-in;transform:perspective(400px) rotateX(-20deg)}60%{opacity:1;transform:perspective(400px) rotateX(10deg)}80%{transform:perspective(400px) rotateX(-5deg)}to{transform:perspective(400px)}}.animate__flipInX{animation-name:flipInX;backface-visibility:visible!important}@keyframes flipInY{0%{animation-timing-function:ease-in;opacity:0;transform:perspective(400px) rotateY(90deg)}40%{animation-timing-function:ease-in;transform:perspective(400px) rotateY(-20deg)}60%{opacity:1;transform:perspective(400px) rotateY(10deg)}80%{transform:perspective(400px) rotateY(-5deg)}to{transform:perspective(400px)}}.animate__flipInY{animation-name:flipInY;backface-visibility:visible!important}@keyframes rollIn{0%{opacity:0;transform:translate3d(-100%,0,0) rotate(-120deg)}to{opacity:1;transform:translateZ(0)}}.animate__rollIn{animation-name:rollIn}@keyframes zoomIn{0%{opacity:0;transform:scale3d(.3,.3,.3)}50%{opacity:1}}.animate__zoomIn{animation-name:zoomIn}@keyframes lightSpeedInLeft{0%{opacity:0;transform:translate3d(-100%,0,0) skewX(30deg)}60%{opacity:1;transform:skewX(-20deg)}80%{transform:skewX(5deg)}to{transform:translateZ(0)}}.animate__lightSpeedInLeft{animation-name:lightSpeedInLeft;animation-timing-function:ease-out}@keyframes lightSpeedInRight{0%{opacity:0;transform:translate3d(100%,0,0) skewX(-30deg)}60%{opacity:1;transform:skewX(20deg)}80%{transform:skewX(-5deg)}to{transform:translateZ(0)}}.animate__lightSpeedInRight{animation-name:lightSpeedInRight;animation-timing-function:ease-out}</style>
<style>.cart-nav-wrapper{align-items:center;background-color:inherit;display:flex;gap:24px}.nav-menu-wrapper.default .branding .logo{position:relative;width:-moz-max-content;width:max-content}</style>
<link rel="preload" as="style" href="https://fonts.googleapis.com/css?family=Lato:100,200,300,400,500,600,700,800,900%7COpen%20Sans:100,200,300,400,500,600,700,800,900%7CMontserrat:100,200,300,400,500,600,700,800,900%7CRed%20Hat%20Display:100,200,300,400,500,600,700,800,900%7CRoboto:100,200,300,400,500,600,700,800,900%7C'Roboto':100,200,300,400,500,600,700,800,900%7CAkshar:100,200,300,400,500,600,700,800,900%7CPoppins:100,200,300,400,500,600,700,800,900%7CPlayfair%20Display:100,200,300,400,500,600,700,800,900%7C'Poppins':100,200,300,400,500,600,700,800,900%7C'Playfair%20Display':100,200,300,400,500,600,700,800,900&display=swap">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/D662KlYJ.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/BrK68jLq.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/BsDc1DTx.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/C6WPgR8Q.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/BoL-Y8dO.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/eorh57EC.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DyCfErH6.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/BxJVBPHt.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/CAPonc__.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/BS0VIWGp.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/D4BEV21w.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DFJdYjI3.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/BKt-LleZ.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/CyKvyFPh.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/b4pqT6fD.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/BeyL6T1v.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DW-3PSLe.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/i4q5ygEu.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DQsHS9en.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DSVo6R7L.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DKeuBCMA.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DfUnjI44.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/Dxzbedgu.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/BICMY8R7.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/Dipz0mOK.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/C3e4t58V.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DMf_iQbA.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/2qUv-9sF.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/Ay-duVFl.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DLrQIorF.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/C30WpvDl.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DM64FhzO.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/DYa7OZHt.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/CGRx5hsb.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/jIk0_C7B.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/wwLfBoTf.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/6ATDtQXf.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/B4ITgbw4.js">
<link  as="script" crossorigin href="https://stcdn.leadconnectorhq.com/_preview/l1uB3ejZ.js">
<link rel="icon" href="https://stcdn.leadconnectorhq.com/funnel/icon/favicon.ico">
<meta name="title" content="AI Lead Follow-Up Automation | Next Level Growth Partner">
<meta property="og:title" content="AI Lead Follow-Up Automation | Next Level Growth Partner">
<meta name="description" content="Instantly convert leads into customers with AI-powered text & voice follow-up. Book more calls. Never miss a lead again. See it live.">
<meta property="og:description" content="Instantly convert leads into customers with AI-powered text & voice follow-up. Book more calls. Never miss a lead again. See it live.">
<meta name="author" content="Oli Wood">
<meta property="og:author" content="Oli Wood">
<meta name="image" content="https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png">
<meta property="og:image" content="https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png">
<meta property="og:type" content="website">
<meta property="twitter:type" content="website">
<script type="module" src="https://stcdn.leadconnectorhq.com/_preview/D662KlYJ.js" crossorigin></script>
<script id="unhead:payload" type="application/json">{"title":""}</script></head><body><div id="__nuxt"><!--[--><!--[--><div class="bgCover bg-fixed"></div><!--[--><!--nuxt-delay-hydration-component--><!--[--><div><!--[--><div><!----><!----><div><div id="nav-menu-popup" style="display:none;" class="hide"><div class="nav-menu-body"><i class="close-menu fas fa-times"></i><ul class="nav-menu"><!--[--><!--]--></ul></div></div></div><div id="overlay" class="hide" style="display:none;background-color:var(--color-lkd4di7k);"><div id="hl_main_popup" class="hide hl_main_popup none borderFull radius5 none popup-body"><span></span><div class="closeLPModal"><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACcAAAAlCAQAAABrYji1AAAC20lEQVRIx5WWr2/bUBDH7+VJTUkKzCqtZAYjMQwJiSoZFIVEhcZppO0PSGALqgDTIivSuDWwSgUBbQMDWkXqyAZaqdJYeJxo1k13zz/jl9p5R+wk/uR73zu/dwBlS0RRspCjHCNAgszc7Y1LIEeHR4fQUMHXKbgyjlGMOZbWw2h5s7oPXoKX1X3w/e+3pxOGaoA6XIIS5uXgzytq1ur+7UyHLOJi2HHnfDGnR9ebKY7QwS520cERTnEZKOTTyTZwG6f8Yl300DJwsYmft6KJLiP/vb+d5YFFHMP64/UGcYqtAiqOFk5ZeQTU4jhNYXbO6b/9nag4fAb+/pICszgBgjwDm+yflcIopuzh0WGccB4nwai3+2PyrFUJ18TXEDFNOMWxNmEKh7S5lWAULiIGL7G+LC7R9lxwyN9xR/GMqb4Mjnyr9X78QvRyP1dLIbzoLvs9fba8YX15nLSA3wKnkA4tL4HlrXDSdBMcZd6ot2FILdLS+JMut9CB1C7QoHRTHDsHQ2rfptZwPSy2A4wCDuyD6zDU4bwE52lw9MwOnC5ZL5esV+g9TlaHgyGVoquFeZmr7PddLkURx6WgRnE1jeLtbJQRIi7m+VIkjdK/225jL6fHK6ibIeLDCBrUHTkcvWLKvYvKL5nDzgmz0Maxe1ePVfeTWNv8p3KusAVQukqfXwlGb/AykJZKVbNBkb6OT808KoVREcLwchBr02yfwqz1Dq6pIGUKfa7x5DbyTWg3d2hIiwpy9UgKZzuKcsGeheHklhPVbu7xOWYoYMdXB+AMXTxNQKfoMoo864+lFSUqPjoYDWnVejCMNaq13mSvJ7e13jZs57FNhyPYMCBk/24xVzqVpsX86vHTV7CFGaUpqg0VhrTAFo6CqoAhDIQDNumqNlRkp6cGGMKUVr0Ntop6W1rCBCPRJfYcyKJhzIiCh7L9B7L8sCgz8eHAWDZ95ifQ0oG2Cm7v2fg/Yh8jlKSDXZEAAAAASUVORK5CYII=" alt="close"></div><div class="drop-zone-draggable"><!----><!--[--><!--[--><div class="row-align-center none noBorder radius0 none c-row c-wrapper row-_2Hy4yY6Hj" id="row-_2Hy4yY6Hj"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="none noBorder radius0 none c-column c-wrapper col-ONIwjdczOL" id="col-ONIwjdczOL"><!----><!----><div class="vertical inner"><!----><!--[--><!--[--><div id="custom-code-CoC46R6UV7" class="c-custom-code c-wrapper custom-code-CoC46R6UV7"><!----><!----><!----><!----><!----><!----><!----><span></span><div id="custom-code-CoC46R6UV7" class="custom-code-container ccustom-code-CoC46R6UV7"><!-- Calendly inline widget begin -->
<div class="calendly-inline-widget" data-url="https://calendly.com/oli-3jx4/discovery-call" style="min-width:320px;height:700px;"></div>
<script type="text/javascript" src="https://assets.calendly.com/assets/external/widget.js" async></script>
<!-- Calendly inline widget end --></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div></div></div><!----><div id="preview-container" class="preview-container hl_page-preview--content"><div><!----><!--[--><!--[--><div class="fullSection noBorder radius0 none c-section c-wrapper section-m0AmIUbB7" style="" id="section-m0AmIUbB7"><!--[--><!----><div class="bg bgCover bg-section-m0AmIUbB7 none" style="border-radius:-2px;position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;"></div><!--]--><!----><div class="inner"><!----><!--[--><!--[--><div class="row-align-center none noBorder radius0 none c-row c-wrapper row-xmdyLrDPJd" id="row-xmdyLrDPJd"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="c-column c-wrapper col-qVAwIKxF0h" id="col-qVAwIKxF0h"><!----><!----><div class="none noBorder radius0 none bg bgCover vertical inner"><!----><!--[--><!--[--><div id="nav-menu-ZXRIR7fv2T" class="c-nav-menu c-wrapper nav-menu-ZXRIR7fv2T"><!----><!----><!----><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><div class="default nav-menu-wrapper cnav-menu-ZXRIR7fv2T pb-menu noBorder radius0 none"><div class="branding"><div class="logo"><div style="cursor:default;" class="image-container pb-menu noBorder radius0 none"><div><div><picture class="hl-image-picture" style="display:block;"><source media="(max-width:900px) and (min-width: 768px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_900/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><source media="(max-width:768px) and (min-width: 640px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_768/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><source media="(max-width:640px) and (min-width: 480px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_640/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><source media="(max-width:480px) and (min-width: 320px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_480/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><source media="(max-width:320px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><img src="https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png" alt="Brand Logo" style="width:230px !important;object-fit:unset;" class="pb-menu noBorder radius0 none hl-optimized mw-100" loading="lazy" data-animation-class=""></picture></div></div></div></div><!----></div><div class="cart-nav-wrapper"><div class="nav-menu-mobile"><span class="menu-icon"></span></div><ul class="nav-menu"><!--[--><li class="nav-menu-item"><a href="#section-sfVzbZHQjy" aria-label="Our Services" target>Our Services</a><!----></li><li class="nav-menu-item"><a href="#section-ay3GWFejuN" aria-label="Revenue Calculator" target>Revenue Calculator</a><!----></li><li class="nav-menu-item"><a href="#section-G_AZ8TUXTY" aria-label="Contact Us" target>Contact Us</a><!----></li><!--]--></ul><!----><!----></div></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="c-column c-wrapper col-rUj1LnNf1n desktop-only" id="col-rUj1LnNf1n"><!----><!----><div class="none noBorder radius0 none bg bgCover vertical inner"><!----><!--[--><!--[--><div id="button-dDRV8gIyYx" class="c-button c-wrapper button-dDRV8gIyYx"><!----><!----><!----><!----><!----><!----><!--[--><!----><!----><button data-animation-class="animate__animated animate__fadeInDown" id="button-dDRV8gIyYx_btn" style="" class="cbutton-dDRV8gIyYx button-shadow2 custom btn-vp btn-hp borderFull radius75 none" aria-label="Book A Discovery Call "><div style="" class="main-heading-group"><div class="button-icon-start"></div><div class="main-heading-button">Book A Discovery Call</div><div class="button-icon-end"></div></div><!----><div style="display:none;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);" class="btn-loader-position"><div style="display:none;" class="v-spinner"><div class="v-moon v-moon1" style="height:30px;width:30px;border-radius:100%;"><div class="v-moon v-moon2" style="height:4.285714285714286px;width:4.285714285714286px;border-radius:100%;top:12.857142857142858px;background-color:rgb(255, 255, 255);"></div><div class="v-moon v-moon3" style="height:30px;width:30px;border-radius:100%;border:4.285714285714286px solid rgb(255, 255, 255);"></div></div></div></div></button><div><!----></div><!----><!----><!--]--><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="about-us fullSection none noBorder radius0 none c-section c-wrapper section-wAGehRwoGr" style="" id="section-wAGehRwoGr"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="row-align-center noBorder radius0 none c-row c-wrapper row-2tWJqvlq4x" id="row-2tWJqvlq4x"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="noBorder radius0 none c-column c-wrapper col-DkXLioh8YD" id="col-DkXLioh8YD"><!----><!----><div class="vertical inner"><!----><!--[--><!--[--><div id="custom-code-G7rpcv-W_V" class="c-custom-code c-wrapper custom-code-G7rpcv-W_V"><!----><!----><!----><!----><!----><!----><!----><span></span><div id="custom-code-G7rpcv-W_V" class="custom-code-container ccustom-code-G7rpcv-W_V"><style>
    /* Import Google Fonts for more punchy typography */
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');
    
    .dashboard-header-container {
      text-align: center;
      padding: 2.5rem 1rem;
      margin: 0 auto;
      max-width: 1200px;
      font-family: 'Poppins', sans-serif;
    }
    
    .dashboard-title {
      font-size: 3rem;
      font-weight: 700;
      margin-bottom: 1.2rem;
      color: #222;
      letter-spacing: -0.03em;
      line-height: 1.2;
      text-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    
    .company-name {
      color: #f97150; 
      font-weight: 800;
      position: relative;
      display: inline-block;
    }
    
    /* Underline effect for company name */
    .company-name::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: -3px;
      width: 100%;
      height: 3px;
      background: linear-gradient(90deg, #f97150, #f08a28);
      border-radius: 2px;
    }
    
    .loading-indicator {
      display: inline-block;
      position: relative;
      width: 80px;
      height: 30px;
    }
    
    .loading-indicator div {
      position: absolute;
      top: 10px;
      width: 13px;
      height: 13px;
      border-radius: 50%;
      background: #f97150;
      animation-timing-function: cubic-bezier(0, 1, 1, 0);
    }
    
    .loading-indicator div:nth-child(1) {
      left: 8px;
      animation: loading1 0.6s infinite;
    }
    
    .loading-indicator div:nth-child(2) {
      left: 8px;
      animation: loading2 0.6s infinite;
    }
    
    .loading-indicator div:nth-child(3) {
      left: 32px;
      animation: loading2 0.6s infinite;
    }
    
    .loading-indicator div:nth-child(4) {
      left: 56px;
      animation: loading3 0.6s infinite;
    }
    
    @keyframes loading1 {
      0% { transform: scale(0); }
      100% { transform: scale(1); }
    }
    
    @keyframes loading2 {
      0% { transform: translate(0, 0); }
      100% { transform: translate(24px, 0); }
    }
    
    @keyframes loading3 {
      0% { transform: scale(1); }
      100% { transform: scale(0); }
    }
    
    .error-message {
      color: #e74c3c;
      font-weight: 600;
      font-size: 1.2rem;
      padding: 0.5rem 1rem;
      background-color: rgba(231, 76, 60, 0.1);
      border-radius: 6px;
      display: inline-block;
    }
    
    /* Subtitle styling */
    .dashboard-subtitle {
      font-family: 'Montserrat', sans-serif;
      font-size: 1.1rem;
      font-weight: 500;
      color: #555;
      margin-top: 0.5rem;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }
    
    /* Media queries for responsive design */
    @media (max-width: 768px) {
      .dashboard-title {
        font-size: 2.5rem;
      }
      
      .dashboard-subtitle {
        font-size: 1rem;
      }
    }
    
    @media (max-width: 480px) {
      .dashboard-title {
        font-size: 2rem;
      }
    }
  </style>
  
  <div class="dashboard-header-container">
    <h1 id="demo-header" class="dashboard-title">
      <div class="loading-indicator"><div></div><div></div><div></div><div></div></div>
    </h1>
    <p id="demo-subtitle" class="dashboard-subtitle" style="display: none;">
      Experience the power of AI-driven conversations tailored for your business
    </p>
  </div>
  
  <script>
    async function loadCompanyHeader() {
      const urlParams = new URLSearchParams(window.location.search);
      const id = urlParams.get('id');
      const headerElement = document.getElementById('demo-header');
      const subtitleElement = document.getElementById('demo-subtitle');
      
      if (!id) {
        headerElement.innerHTML = '<span class="error-message">Missing ID in URL</span>';
        return;
      }
      
      try {
        // Replace this with your actual N8N webhook URL
        const webhookUrl = `https://automate.axonflash.com/webhook/120a5caf-5662-4dad-a148-16ccea75b8cd?id=${id}`;
        
        const response = await fetch(webhookUrl);
        if (!response.ok) throw new Error('Network response was not ok');
        const data = await response.json();
        const company = data.client_company || 'Unknown Company';
        
        // Set the header with the company name in a different color
        headerElement.innerHTML = `Say Hello to Bella <br>Built for <span class="company-name">${company}</span>`;
        
        // Show the subtitle
        subtitleElement.style.display = 'block';
      } catch (error) {
        console.error('Failed to load company data:', error);
        headerElement.innerHTML = '<span class="error-message">Error loading company info</span>';
      }
    }
    
    window.addEventListener('DOMContentLoaded', loadCompanyHeader);
  </script></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="row-align-center none noBorder radius0 none c-row c-wrapper row-N6rakETuRp" id="row-N6rakETuRp"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="c-column c-wrapper col-eosBjsQX8W" id="col-eosBjsQX8W"><!----><!----><div class="none borderFull radius50 none bg bgNoRepeat vertical inner"><!----><!--[--><!--[--><div id="custom-code--_R3OxCxtx" class="c-custom-code c-wrapper custom-code--_R3OxCxtx"><!----><!----><!----><!----><!----><!----><!----><span></span><div id="custom-code--_R3OxCxtx" class="custom-code-container ccustom-code--_R3OxCxtx"><style>
  /* Import Google Fonts for typography */
  @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');

  .ai-dashboard-info-container {
    max-width: 650px;
    margin: 0 auto;
    padding: 2rem 1.2rem;
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    font-family: 'Poppins', sans-serif;
  }

  .ai-demo-info-section {
    text-align: center;
  }

  .ai-info-heading {
    font-size: 2rem;
    font-weight: 700;
    color: #222;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    letter-spacing: -0.03em;
    position: relative;
    display: inline-block;
  }

  .ai-info-heading::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, #f97150, #f08a28);
    border-radius: 2px;
  }

  .ai-info-text {
    font-family: 'Montserrat', sans-serif;
    font-size: 1rem;
    line-height: 1.6;
    color: #444;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .ai-info-points {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .ai-info-point {
    display: flex;
    text-align: left;
    gap: 1rem;
    padding: 1.2rem;
    border-radius: 10px;
    background-color: #f8f9fa;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid #f97150;
  }

  .ai-info-point:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);
  }

  .ai-info-icon {
    flex-shrink: 0;
    width: 45px;
    height: 45px;
    border-radius: 10px;
    background: linear-gradient(135deg, #f97150, #f08a28);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    box-shadow: 0 4px 8px rgba(249, 113, 80, 0.3);
  }

  .ai-info-content {
    flex: 1;
  }

  .ai-info-content h3 {
    color: #222;
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    font-weight: 600;
    letter-spacing: -0.01em;
  }

  .ai-info-content p {
    color: #555;
    font-family: 'Montserrat', sans-serif;
    font-size: 0.9rem;
    line-height: 1.5;
  }

  /* Add emphasis to key words */
  .ai-highlight {
    color: #f97150;
    font-weight: 600;
  }

  .ai-cta-preview {
    background-color: #f5f8fd;
    padding: 1.5rem;
    border-radius: 12px;
    border-left: 5px solid #f97150;
    margin-top: 0.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }

  .ai-cta-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    line-height: 1.3;
  }

  .ai-cta-button-secondary {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #f97150, #f08a28);
    color: white;
    font-weight: 600;
    font-size: 0.95rem;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(249, 113, 80, 0.3);
    letter-spacing: 0.5px;
    text-transform: uppercase;
    cursor: pointer;
    border: none;
  }

  .ai-cta-button-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(249, 113, 80, 0.4);
    background: linear-gradient(135deg, #e85a38, #e07718);
  }

  /* Modal Styles */
  .delegate-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .delegate-modal.active {
    display: flex;
    opacity: 1;
    align-items: center;
    justify-content: center;
  }

  .delegate-modal-content {
    background-color: #fff;
    padding: 2rem;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    position: relative;
    transform: translateY(50px);
    transition: transform 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  }

  .delegate-modal.active .delegate-modal-content {
    transform: translateY(0);
  }

  .close-button {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0.5rem;
    line-height: 1;
  }

  .close-button:hover {
    color: #333;
  }

  .modal-header {
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .modal-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #222;
    margin-bottom: 0.5rem;
    font-family: 'Poppins', sans-serif;
  }

  .modal-subtitle {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 1rem;
    font-family: 'Montserrat', sans-serif;
  }

  .modal-description {
    font-size: 1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
    font-family: 'Montserrat', sans-serif;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Montserrat', sans-serif;
    transition: border-color 0.3s ease;
  }

  .form-group input:focus,
  .form-group textarea:focus,
  .form-group select:focus {
    border-color: #f97150;
    outline: none;
    box-shadow: 0 0 0 3px rgba(249, 113, 80, 0.1);
  }

  .form-group textarea {
    min-height: 100px;
    resize: vertical;
  }

  /* Phone input group styling */
  .phone-input-group {
    display: flex;
    gap: 0.5rem;
  }

  .country-code-select {
    flex: 0 0 140px;
  }

  .phone-number-input {
    flex: 1;
  }

  .submit-button {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #f97150, #f08a28);
    color: white;
    font-weight: 600;
    font-size: 1rem;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Poppins', sans-serif;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .submit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(249, 113, 80, 0.4);
    background: linear-gradient(135deg, #e85a38, #e07718);
  }

  .submit-button.loading {
    opacity: 0.8;
    cursor: not-allowed;
  }

  .success-message, .error-message {
    text-align: center;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-family: 'Montserrat', sans-serif;
  }

  .success-message {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
  }

  .error-message {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
  }

  @media (max-width: 768px) {
    .ai-info-point {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
    
    .ai-info-icon {
      margin-bottom: 0.75rem;
    }
    
    .ai-info-heading {
      font-size: 1.8rem;
    }
    
    .ai-cta-button-secondary {
      padding: 0.7rem 1.4rem;
      font-size: 0.9rem;
    }

    .delegate-modal-content {
      padding: 1.5rem;
    }

    .modal-title {
      font-size: 1.5rem;
    }

    .modal-subtitle {
      font-size: 1.1rem;
    }

    .phone-input-group {
      flex-direction: column;
    }

    .country-code-select {
      flex: 1;
    }
  }
</style>

<div class="ai-dashboard-info-container">
  <div class="ai-demo-info-section">
    <h2 class="ai-info-heading">This Is Just The Beginning</h2>
    
    <p class="ai-info-text">
      What you're seeing now is just <span class="ai-highlight">scratching the surface</span> of what Bella can do. This demo showcases AI-powered conversations that can transform your lead engagement.
    </p>
    
    <div class="ai-info-points">
      <div class="ai-info-point">
        <div class="ai-info-icon">
          <i class="fas fa-puzzle-piece"></i>
        </div>
        <div class="ai-info-content">
          <h3>Custom-Built For Your Business</h3>
          <p>We tailor Bella to your industry, products, and sales process to maximize your unique value proposition.</p>
        </div>
      </div>
      
      <div class="ai-info-point">
        <div class="ai-info-icon">
          <i class="fas fa-chart-line"></i>
        </div>
        <div class="ai-info-content">
          <h3>Focused On Your ROI</h3>
          <p>Our success is measured by your <span class="ai-highlight">bottom line</span>. We optimise for higher conversion rates and qualified leads.</p>
        </div>
      </div>
    
      <div class="ai-cta-preview">
        <p class="ai-cta-text">Ready to see how Bella can be built for your business?</p>
        <button onclick="openDelegateModal()" class="ai-cta-button-secondary">DELEGATE IT IN ONE CLICK!</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal for delegating to colleague -->
<div id="delegateModal" class="delegate-modal">
  <div class="delegate-modal-content">
    <button class="close-button" onclick="closeDelegateModal()">&times;</button>
    
    <div class="modal-header">
      <h2 class="modal-title">Delegate It in One Click!</h2>
      <h3 class="modal-subtitle">Let the Right Team Member Take Over</h3>
      <p class="modal-description">
        See the potential, but you're not the one who'll set it up?<br>
        Send this to the right person on your team — we'll take it from there.
      </p>
    </div>
    
    <div id="messageContainer"></div>
    
    <form id="delegateForm" onsubmit="submitDelegateForm(event)">
      <div class="form-group">
        <label for="colleague-name">Colleague's Name *</label>
        <input type="text" id="colleague-name" required>
      </div>
      
      <div class="form-group">
        <label for="colleague-email">Email Address *</label>
        <input type="email" id="colleague-email" required>
      </div>
      
      <div class="form-group">
        <label for="colleague-phone">Phone Number *</label>
        <div class="phone-input-group">
          <select id="country-code" class="country-code-select" required>
            <option value="+1">USA (+1)</option>
            <option value="+44">UK (+44)</option>
            <option value="+61">AUS (+61)</option>
            <option value="+1">CAN (+1)</option>
            <option value="+33">FRA (+33)</option>
            <option value="+49">DEU (+49)</option>
            <option value="+39">ITA (+39)</option>
            <option value="+34">ESP (+34)</option>
            <option value="+31">NLD (+31)</option>
            <option value="+46">SWE (+46)</option>
            <option value="+47">NOR (+47)</option>
            <option value="+45">DNK (+45)</option>
            <option value="+358">FIN (+358)</option>
            <option value="+41">CHE (+41)</option>
            <option value="+43">AUT (+43)</option>
            <option value="+32">BEL (+32)</option>
            <option value="+353">IRL (+353)</option>
            <option value="+351">PRT (+351)</option>
            <option value="+48">POL (+48)</option>
            <option value="+420">CZE (+420)</option>
            <option value="+36">HUN (+36)</option>
            <option value="+30">GRC (+30)</option>
            <option value="+7">RUS (+7)</option>
            <option value="+81">JPN (+81)</option>
            <option value="+86">CHN (+86)</option>
            <option value="+82">KOR (+82)</option>
            <option value="+91">IND (+91)</option>
            <option value="+65">SGP (+65)</option>
            <option value="+60">MYS (+60)</option>
            <option value="+62">IDN (+62)</option>
            <option value="+66">THA (+66)</option>
            <option value="+84">VNM (+84)</option>
            <option value="+63">PHL (+63)</option>
            <option value="+64">NZL (+64)</option>
            <option value="+55">BRA (+55)</option>
            <option value="+52">MEX (+52)</option>
            <option value="+54">ARG (+54)</option>
            <option value="+56">CHL (+56)</option>
            <option value="+57">COL (+57)</option>
            <option value="+58">VEN (+58)</option>
            <option value="+51">PER (+51)</option>
            <option value="+971">ARE (+971)</option>
            <option value="+966">SAU (+966)</option>
            <option value="+27">ZAF (+27)</option>
            <option value="+234">NGA (+234)</option>
            <option value="+20">EGY (+20)</option>
          </select>
          <input type="tel" id="colleague-phone" class="phone-number-input" placeholder="Phone number" required>
        </div>
      </div>
      
      <div class="form-group">
        <label for="personal-message">Personal Message (optional)</label>
        <textarea id="personal-message" placeholder="Add a personal note to your colleague..."></textarea>
      </div>
      
      <button type="submit" class="submit-button" id="submitButton">
        ➡️ Send to My Colleague
      </button>
    </form>
  </div>
</div>

<!-- Add Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />

<script>
function openDelegateModal() {
  const modal = document.getElementById('delegateModal');
  modal.classList.add('active');
  document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function closeDelegateModal() {
  const modal = document.getElementById('delegateModal');
  modal.classList.remove('active');
  document.body.style.overflow = ''; // Restore scrolling
}

function getIdFromUrl() {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('id');
}

function showMessage(message, isError = false) {
  const messageContainer = document.getElementById('messageContainer');
  messageContainer.innerHTML = `
    <div class="${isError ? 'error-message' : 'success-message'}">
      ${message}
    </div>
  `;
}

async function submitDelegateForm(event) {
  event.preventDefault();
  
  const submitButton = document.getElementById('submitButton');
  const messageContainer = document.getElementById('messageContainer');
  
  // Clear any previous messages
  messageContainer.innerHTML = '';
  
  // Disable button and show loading state
  submitButton.disabled = true;
  submitButton.classList.add('loading');
  submitButton.innerHTML = '⏳ Sending...';
  
  // Get form values
  const name = document.getElementById('colleague-name').value;
  const email = document.getElementById('colleague-email').value;
  const countryCode = document.getElementById('country-code').value;
  const phoneNumber = document.getElementById('colleague-phone').value;
  const message = document.getElementById('personal-message').value;
  const demoId = getIdFromUrl();
  
  // Combine country code and phone number
  const fullPhoneNumber = `${countryCode}${phoneNumber.replace(/^\+/, '')}`;
  
  // Prepare data for sending
  const formData = {
    colleagueName: name,
    colleagueEmail: email,
    colleaguePhone: fullPhoneNumber,
    personalMessage: message,
    demoId: demoId,
    // Add current page URL for reference
    currentUrl: window.location.href
  };
  
  try {
    const response = await fetch('https://automate.axonflash.com/webhook/94e6a685-41f3-41e9-ae67-7db7abc95027', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData)
    });
    
    if (response.ok) {
      // Success
      showMessage('Demo successfully sent to your colleague!');
      
      // Reset form
      document.getElementById('delegateForm').reset();
      
      // Close modal after delay
      setTimeout(() => {
        closeDelegateModal();
        messageContainer.innerHTML = '';
      }, 2000);
    } else {
      // Error
      const errorData = await response.json().catch(() => null);
      const errorMessage = errorData?.message || 'An error occurred while sending the information.';
      showMessage(errorMessage, true);
    }
  } catch (error) {
    console.error('Error:', error);
    showMessage('Unable to connect to the server. Please try again later.', true);
  } finally {
    // Re-enable button and restore original text
    submitButton.disabled = false;
    submitButton.classList.remove('loading');
    submitButton.innerHTML = '➡️ Send to My Colleague';
  }
}

// Close modal when clicking outside
window.onclick = function(event) {
  const modal = document.getElementById('delegateModal');
  if (event.target === modal) {
    closeDelegateModal();
  }
}

// Close modal on escape key
document.addEventListener('keydown', function(event) {
  if (event.key === 'Escape') {
    closeDelegateModal();
  }
});
</script></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="c-column c-wrapper col-V1zVpPfYjM" id="col-V1zVpPfYjM"><!----><!----><div class="none borderFull radius50 none bg bgNoRepeat vertical inner"><!----><!--[--><!--[--><div id="custom-code-F-9ZM6pPpK" class="c-custom-code c-wrapper custom-code-F-9ZM6pPpK"><!----><!----><!----><!----><!----><!----><!----><span></span><div id="custom-code-F-9ZM6pPpK" class="custom-code-container ccustom-code-F-9ZM6pPpK"><link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
<style>
    @import url("https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;700&display=swap");

    .info {
        font-size: 1.3rem;
        margin: 1rem;
    }

    .title {
        font-size: 2rem;
        margin: 2rem 0;
    }

    .info-text {
        color: rgba(0, 0, 0, 0.575);
        max-width: 25rem;
        text-align: justify;
    }

    .container {
        display: flex;
        flex-direction: column; /* Changed to column to add notification text below */
        justify-content: center;
        align-items: center;
    }

    .phone-parent-container {
        border: solid 0.5rem rgb(18, 18, 18);
        border-radius: 40px;
        box-shadow: 0 0 20px 0 rgba(255, 255, 255, 0.461);
        height: 40rem;
        width: 18.8rem;
        background: hsl(0, 0%, 100%);
        margin-top: 1rem;
        position: relative; /* Added for glow effect positioning */
        transition: box-shadow 0.5s ease; /* Smooth transition for glow effect */
    }

    /* Glow effects */
    .glow-blue {
        box-shadow: 0 0 25px 5px rgba(0, 123, 255, 0.7);
    }

    .glow-green {
        box-shadow: 0 0 25px 5px rgba(40, 167, 69, 0.7);
    }

    .glow-yellow {
        box-shadow: 0 0 25px 5px rgba(255, 193, 7, 0.7);
    }

    .glow-red {
        box-shadow: 0 0 25px 5px rgba(255, 32, 7, 0.7);
    }

    .glow-purple {
        box-shadow: 0 0 25px 5px rgba(255, 7, 243, 0.7);
    }

    /* Notification text below phone */
    .notification-text {
        margin-top: 1.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-family: "Rubik", sans-serif;
        font-weight: 500;
        background-color: #f8f9fa;
        border-left: 5px solid #007bff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        opacity: 0;
        transform: translateY(10px);
        transition: opacity 0.5s ease, transform 0.5s ease;
        max-width: 80%;
        text-align: center;
    }

    .notification-visible {
        opacity: 1;
        transform: translateY(0);
    }

    /* Confetti container */
    .confetti-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
    }

    .phone-child-container {
        border-radius: 0.9375rem;  
        height: inherit;
    }

    .footer {
        margin: 2rem 0;
        text-align: center;
    }

    a {
        color: #000;
        text-decoration: none;
    }

    @media(max-width: 725px) {
        .small-only {
            display: block;
        }

        .container {
            flex-direction: column;
        }

        .title {
            text-align: center;
        }
    }

    .phone-header {
        background-color: rgba(233, 233, 235, 255);
        border-radius: 40px 40px 0 0;
        box-shadow: 0 0 0.625rem rgba(0, 0, 0, 0.1);
        color: #000;
        display: flex;
        flex-direction: column; /* Organize top and bottom layers */
        align-items: center;
        justify-content: flex-start;
        height: 5rem;
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-size: 0.9rem;
        position: relative; /* For absolute positioning of camera module */
        padding: 0 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* Thin black border at the bottom */
    }

    .phone-header-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding-top: 0.5rem;
    }

    .phone-header-time {
        font-size: 0.75rem;
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        margin-left: 1rem;
        margin-top: 0.2rem;
        font-weight: bold; /* Makes the icons' text bold */
    }

    /* Camera Module */
    .phone-header-camera {
        background-color: #1b1a1a;
        border-radius: 1rem;
        height: 1.2rem;
        width: 5rem;
        position: absolute; /* Position independently */
        top: 0.5rem; /* Adjust vertical alignment */
        left: 50%; /* Center horizontally */
        transform: translateX(-50%); /* Offset to truly center the module */
        z-index: 1; /* Ensure it appears above other elements */
    }

    .phone-header-icons {
        display: flex; /* Ensures the icons are in a row */
        align-items: center; /* Aligns the icons vertically */
        gap: 0.4rem; /* Adds space between the icons */
    }

    .phone-header-icons i {
        font-size: 0.7rem; /* Adjust the size of the icons */
        line-height: 1; /* Ensure consistent alignment */
        color: #000; /* Optional: Set icon color */
    }

    .phone-header-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        margin-top: 1rem; /* Space between top and bottom layers */
    }

    .back-button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 2rem;
        height: 2rem;
        background-color: transparent; /* Transparent background */
        border-radius: 50%;
        cursor: pointer;
        color: #007bff; /* Blue color for the icon */
        font-size: 1.2rem;
    }

    .back-button:hover {
        background-color: rgba(0, 123, 255, 0.1); /* Subtle hover effect */
    }

    .back-button i {
        font-size: 0.9rem; /* Adjust the size of the icons */
        line-height: 1; /* Ensure consistent alignment */
    }

    .phone-header-name {
        font-size: 1rem;
        text-align: center;
        flex: 1; /* Center the name between back and more info */
    }

    .video-button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 2rem;
        height: 2rem;
        background-color: transparent; /* Transparent background */
        border-radius: 50%;
        cursor: pointer;
        color: #007bff; /* Blue color for the icon */
        font-size: 1.2rem;
    }

    .video-button:hover {
        background-color: rgba(0, 123, 255, 0.1); /* Subtle hover effect */
    }

    .video-button i {
        font-size: 0.9rem; /* Adjust the size of the icons */
        line-height: 1; /* Ensure consistent alignment */
    }

    .phone-content {
        height: 31rem; /* Ensure fixed height */
        margin: 0 0 0 0;
        overflow-y: auto; /* Enable vertical scrolling */
        display: flex;
        flex-direction: column; /* Keep messages in order */
        gap: 0.5rem; /* Add space between messages */
    }

    /* For modern browsers like Chrome, Edge, Safari */
    .phone-content::-webkit-scrollbar {
        display: none; /* Hide scrollbar */
    }

    /* For Firefox */
    .phone-content {
        scrollbar-width: none; /* Hide scrollbar */
    }

    /* For Internet Explorer (just in case) */
    .phone-content {
        -ms-overflow-style: none; /* Hide scrollbar */
    }

    .msg {
        border-radius: 16px; /* Rounded corners for bubble shape */
        box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0); /* Subtle shadow */
        font-size: 1rem;
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; /* Font stack */
        margin-top: 0.4rem;
        margin-bottom: 0.4rem;
        padding: 0.5rem; /* Padding inside the bubble */
        display: inline-block; /* Allow the width to adjust based on content */
        max-width: 70%; /* Maximum width for longer messages */
        word-wrap: break-word; /* Wrap text for longer messages */
        position: relative;
        opacity: 0;
        transition: opacity 400ms ease, transform 400ms ease;
    }

    /* Received Messages */
    .msg-received {
        background-color: rgba(233, 233, 235, 255); /* iMessage gray */
        color: #000000; /* Black text */
        align-self: flex-start; /* Align to the left */
        margin-left: 10px;
        border-radius: 16px 16px 16px 0; /* Bubble shape for received messages */
    }

    /* Sent Messages */
    .msg-sent {
        background-color: rgba(0, 122, 255, 255); /* iMessage blue */
        color: #ffffff; /* White text */
        align-self: flex-end; /* Align to the right */
        margin-right: 10px;
        border-radius: 16px 16px 0 16px; /* Bubble shape for received messages */
    }

    /* Add smooth appearance for messages */
    .show {
        position: relative;
        opacity: 1;
        transform: translateY(0);
    }

    .phone-footer {
        margin: 0.2rem;
        justify-content: center;
        align-items: center;
        position: relative; /* Enables offset positioning */
        top: 0rem; /* 0.2rem for local. 0rem for delpoyed */
        border-radius: 1rem;
    }

    .footer-container {
        position: relative;
        background: none; /* Ensure no background is applied */
        box-shadow: none; /* Remove any shadows if present */
        border: none; /* Remove any borders if present */
    }

    .message-box {
        display: flex; /* Flexbox to align text area and send button */
        align-items: center; /* Center the button and text vertically */
        border: 1px solid #ddd;
        border-radius: 20px;
        padding: 5px 10px; /* Add padding for better spacing */
        background-color: rgba(240,240,240,255);
        position: relative;
        height: 2rem; /* Increase height for a single line message */
    }

    .enter-message {
        flex: 1; /* Allow the text area to grow */
        border: none; /* Remove border */
        outline: none; /* Remove focus outline */
        resize: none; /* Disable manual resizing */
        font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; /* Font stack */
        font-size: 1rem;
        /* border-radius: 20px; */
        line-height: 1.5; /* Adjust line spacing for readability */
        padding: 0.5px; /* Add padding inside the input box */
        overflow: hidden; /* Prevent scrollbar */
        height: 1.5rem; /* Match initial height for one line */
        max-height: 8rem; /* Optional: Set a maximum height */
        background: none;
        text-align: left;
    }

    .send-button {
        background-color: rgba(0, 122, 255, 1);
        border: none;
        border-radius: 1rem;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 1.7rem;
        width: 3rem;
        margin-left: 10px;
        color: white;
        font-size: 1rem;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        text-align: center;
        user-select: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        transition: background-color 0.3s ease, transform 0.1s ease;
    }


    .send-button:hover {
        background-color: #0056b3; /* Darker blue for hover */
    }

    .send-button:active {
        background-color: #004085; /* Even darker blue for active state */
        transform: scale(0.98); /* Add a slight "press" effect */
    }

    .send-button i {
        margin: 0; /* Ensure icon is centered */
    }


    /* Loading Message (Styled as Received Message) */
    .msg-received.loading {
        display: flex; /* Flexbox for the dots */
        justify-content: center; /* Center dots inside the bubble */
        align-items: center; /* Vertically center dots */
        background-color: rgba(233, 233, 235, 0.9); /* Slightly transparent gray */
        max-width: 70%; /* Keep the same max width as received messages */
        padding: 0.5rem; /* Consistent padding */
        font-size: 0; /* Hide any unwanted text */
    }

    /* Dots for Typing Animation */
    .msg-received.loading .dot {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin: 0 2px;
        background-color: rgba(0, 0, 0, 0.6); /* Gray dots for typing */
        border-radius: 50%;
        animation: dot-bounce 0.6s infinite alternate; /* Typing effect */
    }

    .msg-received.loading .dot:nth-child(1) {
        animation-delay: 0s;
    }
    .msg-received.loading .dot:nth-child(2) {
        animation-delay: 0.2s;
    }
    .msg-received.loading .dot:nth-child(3) {
        animation-delay: 0.4s;
    }

    /* Dot animation */
    @keyframes dot-bounce {
        from {
            transform: translateY(0);
        }
        to {
            transform: translateY(-6px);
        }
    }
</style>
   
<main>
    <div class="container">
        <div class="phone-parent-container" id="phone-container">
            <div class="phone-child-container">
                <div class="phone-header">
                    <div class="phone-header-top">
                        <!-- Time on the left -->
                        <div id="phone-header-time" class="phone-header-time">10:15</div>
                        <!-- Camera cutout in the center -->
                        <div class="phone-header-camera"></div>
                    
                        <!-- Icons on the right -->
                        <div class="phone-header-icons">
                            <i class="fa fa-signal"></i>
                            <i class="fa fa-wifi"></i>
                            <i class="fa fa-battery"></i>
                        </div>
                    </div>
                    <!-- Bottom Layer: Back Button, Name, More Info -->
                    <div class="phone-header-bottom">
                        <div class="back-button">
                            <i class="fa-solid fa-chevron-left"></i>
                        </div>
                        <div class="phone-header-name">Bella</div>
                        <div class="video-button">
                            <i class="fa fa-video"></i>
                        </div>
                    </div>
                </div>
                <div class="phone-content">
                </div>
                <div class="phone-footer">
                    <div class="footer-container">
                        <div class="message-box">
                            <textarea placeholder="Type a message..." class="enter-message"></textarea>
                            <div class="send-button">
                                Send
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Notification text element -->
        <div class="notification-text" id="notification-text">
            Notification will appear here
        </div>
    </div>
    <!-- Container for confetti -->
    <div class="confetti-container" id="confetti-container"></div>
</main>
<script>
    const messages = document.getElementsByClassName("msg");
    const phoneContent = document.querySelector(".phone-content");
    const messageInput = document.querySelector(".enter-message");
    const sendButton = document.querySelector(".send-button");
    const textArea = document.querySelector('.enter-message');
    const messageBox = document.querySelector('.message-box');
    const phoneContainer = document.getElementById('phone-container');
    const notificationText = document.getElementById('notification-text');
    const salesRepImage = "https://i.ibb.co/yFGncKgm/Grace.png"
    const customerImageMale = "https://i.ibb.co/spcmnhrZ/icons8-male-user-96.png"
    const customerImageFemale = "https://i.ibb.co/spcmnhrZ/icons8-male-user-96.png"
    const params = new URLSearchParams(window.location.search);
    const demoId = params.get('id');
    const requestURL = "https://automate.axonflash.com/webhook/2dbcbb31-a950-4987-95f5-e2136a33e391?id=" + demoId;
    const time_ms = Date.now();
    
    // Wait for response
    let isWaitingForResponse = true;
    sendButton.disabled = true;
    let isFirstUserMessage = true;
    let customerImage = customerImageMale; // Default image

    let conversationData = {
        conversationHistory: "[SYSTEM MESSAGE] Start conversation",
        chatGPT: "",
        conversationID: demoId + '_' + time_ms,
        predictedGender: "male" // default
    }

    let original_height = textArea.scrollHeight;

    // Display preloaded messages with animation
    for (let i = 0; messages.length > i; i++) {
        setTimeout(() => {
            messages[i].classList.add("show");
            scrollToBottom();
        }, i * 1000);
    }

    // Function to add a message to the chat
    function addMessage(text, isUser = true) {
        const messageDiv = document.createElement("div");
        messageDiv.classList.add("msg", isUser ? "msg-sent" : "msg-received");
        
        // Create a container for the message and image
        const messageContainer = document.createElement("div");
        messageContainer.style.display = "flex";

        if (isUser) {
            // For sent messages: align to the right
            messageContainer.style.justifyContent = "flex-end";
            // Image for sent messages
            const userImage = document.createElement("img");
            userImage.src = customerImage; 
            userImage.alt = "User";
            userImage.style.borderRadius = "50%";
            userImage.style.width = "2.5rem";
            userImage.style.height = "2.5rem";
            userImage.style.alignSelf = "flex-end"; // Ensure image is aligned at the bottom
            userImage.style.marginRight = "2px"
            messageContainer.appendChild(messageDiv);
            messageContainer.appendChild(userImage); // Add image to the right
            
            // Check if this is the first user message
            if (isFirstUserMessage) {
                isFirstUserMessage = false;
                activateGlowEffect('blue', 'Conversation started: The lead has engaged with ' + 'Bella.');
            }
        } else {
            // For received messages: align to the left
            messageContainer.style.justifyContent = "flex-start";
            // Image for received messages
            const receivedImage = document.createElement("img");
            receivedImage.src = salesRepImage;
            receivedImage.alt = "AI";
            receivedImage.style.borderRadius = "50%";
            receivedImage.style.width = "2.5rem";
            receivedImage.style.height = "2.5rem";
            receivedImage.style.alignSelf = "flex-end"; // Ensure image is aligned at the bottom
            receivedImage.style.marginLeft = "2px"
            messageContainer.appendChild(receivedImage); // Add image to the left
            messageContainer.appendChild(messageDiv);
            
            // Check for trigger phrases in AI response
            checkForTriggerPhrases(text);
            
        }

        messageDiv.innerHTML = text.replace(/\n/g, "<br>");

        // Add animation class after timeout
        setTimeout(() => {
            messageDiv.classList.add("show");
            scrollToBottom(); // Scroll after the message is added
        }, 10);

        // Append the message to the chat window
        phoneContent.appendChild(messageContainer);
    }

    // Function to check for trigger phrases in the AI responses
    function checkForTriggerPhrases(text) {
        const lowerText = text.toLowerCase();

        if (lowerText.includes('from our list')) {
            activateGlowEffect('red', 'Conversation ended: The lead does not want to be contacted any longer');
            disableMessaging();
        } 
        else if (lowerText.includes('expect a call')) {
            activateGlowEffect('green', 'Lead qualified: This lead will be sent instantly to your sales team.');
            triggerConfetti();
        }
        else if (lowerText.includes('have a great day')) {
            activateGlowEffect('yellow', 'Conversation ended: The lead has completed their interaction with ' + 'Bella.');
            disableMessaging();
        }
        else if (lowerText.includes('notified the team')) {
            activateGlowEffect('purple', 'Conversation milestone: ' + 'Bella' + ' will notify your team to follow up with the lead for the specific information');
        } 
    }

    // Function to disable messaging when conversation ends
    function disableMessaging() {
        // Disable the text area
        messageInput.disabled = true;
        messageInput.style.opacity = "0.5";
        messageInput.placeholder = "Conversation ended";
        
        // Disable the send button
        sendButton.disabled = true;
        sendButton.style.opacity = "0.5";
        sendButton.style.backgroundColor = "#cccccc";
        
        // Make sure enter key doesn't trigger sending
        isWaitingForResponse = true;
    }

    // Function to activate glow effect
    function activateGlowEffect(color, message) {
        // Remove any existing glow classes
        phoneContainer.classList.remove('glow-blue', 'glow-green', 'glow-yellow', 'glow-red', 'glow-purple');
        
        // Add the new glow class
        phoneContainer.classList.add(`glow-${color}`);
        
        // Show notification with message
        notificationText.textContent = message;
        notificationText.classList.add('notification-visible');
        
        // Remove the glow effect after 5 seconds
        setTimeout(() => {
            phoneContainer.classList.remove(`glow-${color}`);
            notificationText.classList.remove('notification-visible');
        }, 7000);
    }

    // Function to trigger confetti animation
    function triggerConfetti() {
        const duration = 3000;
        const animationEnd = Date.now() + duration;
        const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

        function randomInRange(min, max) {
            return Math.random() * (max - min) + min;
        }

        const interval = setInterval(function() {
            const timeLeft = animationEnd - Date.now();

            if (timeLeft <= 0) {
                return clearInterval(interval);
            }

            const particleCount = 50 * (timeLeft / duration);
            
            // Confetti burst from center
            confetti({
                ...defaults,
                particleCount,
                origin: { x: 0.5, y: 0.5 }
            });
            
            // Confetti from sides
            confetti({
                ...defaults,
                particleCount: particleCount * 0.5,
                origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
            });
            
            confetti({
                ...defaults,
                particleCount: particleCount * 0.5,
                origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
            });
        }, 250);
    }

    // Event listener for the Enter key
    messageInput.addEventListener("keydown", (e) => {
        if (e.key === "Enter" && !isWaitingForResponse) {
            sendButton.click();
        }
    });

    // Function to scroll the chat to the bottom
    function scrollToBottom() {
        phoneContent.scrollTo({
            top: phoneContent.scrollHeight,
            behavior: "smooth",
        });
    }

    function resizeMessageContainer(){
        // textArea.style.height = 'auto';
        const scrollHeight = textArea.scrollHeight;

        // Update the height of the textarea and message box
        textArea.style.height = `${scrollHeight}px`;
        messageBox.style.height = `${scrollHeight + 8}px`; // Adjust for padding

        // Shift the message box upwards
        const footerContainer = document.querySelector('.footer-container');
        footerContainer.style.transform = `translateY(-${scrollHeight - 24}px)`;
    }

    function resetMessageHeight(){
        // Update the height of the textarea and message box
        textArea.style.height = `${original_height}px`;
        messageBox.style.height = `${original_height + 8}px`; // Adjust for padding

        // Shift the message box upwards
        const footerContainer = document.querySelector('.footer-container');
        footerContainer.style.transform = `translateY(-${original_height - 24}px)`;
    }

    textArea.addEventListener('input', () => {
        resizeMessageContainer()
    });

    textArea.addEventListener('keydown', (event) => {
        if (event.key === 'Enter') {
            event.preventDefault(); // Stop the default behavior (adding a new line)
        }
    }); 


    function updateTime() {
        const timeElement = document.getElementById("phone-header-time");
        const now = new Date();

        // Format hours and minutes
        const hours = now.getHours().toString().padStart(2, "0");
        const minutes = now.getMinutes().toString().padStart(2, "0");

        // Update the time in the header
        timeElement.textContent = `${hours}:${minutes}`;
    }

    // Call the function initially to set the current time
    updateTime();

    // Update the time every minute
    setInterval(updateTime, 60000); // Updates every 60 seconds

    // Event listener for the send button
    sendButton.addEventListener("click", () => {
        const userMessage = messageInput.value.trim();
        if (userMessage && !isWaitingForResponse) {
            // Set waiting state
            isWaitingForResponse = true;
            sendButton.disabled = true;

            addMessage(userMessage); // Add the user's message
            // Append user's message to the conversation history
            conversationData["conversationHistory"] += ` | User: ${userMessage}`; 
            sendMessageToWebhook(conversationData);
            messageInput.value = ""; // Clear the input field
            resetMessageHeight();
        }
    });

    function addLoadingMessage() {
        const loadingDiv = document.createElement("div");
        loadingDiv.classList.add("msg", "msg-received", "loading", "show");

        // Add the dots for the typing animation
        loadingDiv.innerHTML = `
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
        `;

        // Create a container for the loading message and image
        const loadingContainer = document.createElement("div");
        loadingContainer.style.display = "flex";
        loadingContainer.style.justifyContent = "flex-start";
        
        // Image for received messages
        const receivedImage = document.createElement("img");
        receivedImage.src = salesRepImage;
        receivedImage.alt = "AI";
        receivedImage.style.borderRadius = "50%";
        receivedImage.style.width = "2.5rem";
        receivedImage.style.height = "2.5rem";
        receivedImage.style.alignSelf = "flex-end"; // Ensure image is aligned at the bottom
        receivedImage.style.marginLeft = "2px";
        
        loadingContainer.appendChild(receivedImage);
        loadingContainer.appendChild(loadingDiv);

        // Append the loading message to the chat
        phoneContent.appendChild(loadingContainer);

        // Scroll to the bottom
        scrollToBottom();
    }

    function removeLoadingMessage() {
        const loadingContainer = phoneContent.querySelector("div:has(.msg-received.loading)");
        if (loadingContainer) {
            loadingContainer.remove();
        }
    }

    function sendMessageToWebhook(message) {
        let messageDelay = 1000;
        
        setTimeout(function() {
            // Add the loading message
            addLoadingMessage();
        }, messageDelay);

        // Send the message to your webhook using fetch
        fetch(requestURL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(message)
            })
            .then(response => response.json())
            .then(data => {
                // Check if the webhook returned a response message
                if (data) {
                    conversationData = data; 
                    const wordCount = conversationData.chatGPT.split(/\s+/).length;
                    const delay = (wordCount * (60 / 1500) * 1000) + messageDelay; // Delay in milliseconds based on 140 words per minute
                    if (conversationData.predictedGender == "female") {
                        customerImage = customerImageFemale;
                    } else {
                        customerImage = customerImageMale;
                    }
                    setTimeout(function() {
                        // Remove the loading message
                        removeLoadingMessage();
                        addMessage(conversationData.chatGPT, false);
                        // Re-enable send after receiving the AI message
                        isWaitingForResponse = false;
                        sendButton.disabled = false;
                    }, delay);
                } else {
                    removeLoadingMessage();
                    addMessage("Sorry, something went wrong.", false);
                    isWaitingForResponse = false;
                    sendButton.disabled = false;
                }
            })
            .catch(error => {
                removeLoadingMessage();
                addMessage("Sorry, something went wrong.", false);
                console.error('Error:', error);
                // Re-enable send after receiving the AI message
                isWaitingForResponse = false;
                sendButton.disabled = false;
            });
    }

    
    sendMessageToWebhook(conversationData);
</script></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="about-us fullSection none noBorder radius0 none c-section c-wrapper section-sfVzbZHQjy" style="" id="section-sfVzbZHQjy"><!--[--><!----><div class="bg bgCover bg-section-sfVzbZHQjy none" style="border-radius:-2px;position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;"></div><!--]--><!----><div class="inner"><!----><!--[--><!--[--><div class="row-align-center noBorder radius0 none c-row c-wrapper row-Vw0H17D-8H" id="row-Vw0H17D-8H"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="noBorder radius0 none c-column c-wrapper col-eMIP6gTw6q" id="col-eMIP6gTw6q"><!----><!----><div class="vertical inner"><!----><!--[--><!--[--><div id="custom-code-D0vHByKM9n" class="c-custom-code c-wrapper custom-code-D0vHByKM9n"><!----><!----><!----><!----><!----><!----><!----><span></span><div id="custom-code-D0vHByKM9n" class="custom-code-container ccustom-code-D0vHByKM9n"><style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');
    
    /* Reset and Base Styles - with unique prefix */
    .ai-features * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: 'Poppins', sans-serif;
    }
    
    /* Container Styles - with unique prefix */
    .ai-features-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    /* Header Section - with unique prefix */
    .ai-features-header {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 40px 30px;
        text-align: center;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    }
    
    .ai-features-header h1 {
        font-size: 2rem;
        margin-bottom: 15px;
        color: #222;
        font-weight: 700;
        letter-spacing: -0.03em;
        line-height: 1.2;
        position: relative;
        display: inline-block;
    }
    
    .ai-features-header h1::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 70px;
        height: 3px;
        background: linear-gradient(90deg, #f97150, #f08a28);
        border-radius: 2px;
    }
    
    .ai-features-header h1 span {
        color: #f97150;
        font-weight: 800;
    }
    
    .ai-features-header p {
        font-family: 'Montserrat', sans-serif;
        font-size: 1rem;
        max-width: 800px;
        margin: 1.5rem auto 0;
        color: #444;
        line-height: 1.6;
    }
    
    /* Features Grid - with unique prefix */
    .ai-features-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
    
    .ai-feature-card {
        background-color: #ffffff;
        border-radius: 12px;
        padding: 35px 30px;
        text-align: center;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        height: 100%;
        display: flex;
        flex-direction: column;
        border-left: 4px solid #f97150;
    }
    
    .ai-feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }
    
    .ai-icon-container {
        margin-bottom: 20px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .ai-feature-icon {
        width: 45px;
        height: 45px;
        border-radius: 10px;
        background: linear-gradient(135deg, #3b82f6, #1e40af);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.4rem;
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
        margin: 0 auto;
        transition: all 0.3s ease;
    }

    .ai-feature-icon:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 12px rgba(59, 130, 246, 0.4);
    }
    
    .ai-feature-card h2 {
        font-size: 1.1rem;
        margin-bottom: 15px;
        color: #222;
        font-weight: 600;
        letter-spacing: -0.01em;
    }
    
    .ai-feature-card p {
        margin-bottom: 20px;
        color: #555;
        font-family: 'Montserrat', sans-serif;
        font-size: 0.9rem;
        line-height: 1.5;
        flex-grow: 1;
    }
    
    .ai-highlight {
        color: #3b82f6;
        font-weight: 600;
    }

    .ai-feature-card {
        transition: all 0.3s ease;
        animation: fadeInUp 0.6s ease-out;
    }

    .ai-feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
    }

    .ai-feature-card:nth-child(1) { animation-delay: 0.1s; }
    .ai-feature-card:nth-child(2) { animation-delay: 0.2s; }
    .ai-feature-card:nth-child(3) { animation-delay: 0.3s; }
    .ai-feature-card:nth-child(4) { animation-delay: 0.4s; }
    
    .ai-feature-card .ai-tagline {
        font-weight: 600;
        color: #222;
        font-size: 0.95rem;
        margin-top: auto;
        padding-top: 15px;
        border-top: 1px solid #eee;
    }
    
    /* Responsive Design - with unique prefix */
    @media (max-width: 992px) {
        .ai-features-header h1 {
            font-size: 1.8rem;
        }
        
        .ai-features-header p {
            font-size: 0.95rem;
        }
    }
    
    @media (max-width: 768px) {
        .ai-features-grid {
            grid-template-columns: 1fr;
        }
        
        .ai-features-container {
            padding: 10px;
        }
        
        .ai-features-header {
            padding: 30px 20px;
        }
        
        .ai-feature-card {
            padding: 30px 20px;
        }
    }
    
    @media (max-width: 480px) {
        .ai-features-header h1 {
            font-size: 1.6rem;
        }
        
        .ai-features-header p {
            font-size: 0.9rem;
        }
    }
    
    /* Button Styles - with unique prefix and modern blue theme */
    .ai-features-cta-button {
        display: inline-block;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(135deg, #3b82f6, #1e40af);
        color: white;
        font-weight: 600;
        font-size: 0.95rem;
        text-decoration: none;
        border-radius: 50px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(59, 130, 246, 0.3);
        letter-spacing: 0.5px;
        text-transform: uppercase;
        border: none;
        cursor: pointer;
        margin-top: 20px;
        position: relative;
        overflow: hidden;
    }

    .ai-features-cta-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 15px rgba(59, 130, 246, 0.4);
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
    }

    .ai-features-cta-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
        z-index: 1;
    }

    .ai-features-cta-button:hover::before {
        left: 100%;
    }
</style>
<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<div class="ai-features">
    <div class="ai-features-container">
        <div class="ai-features-header">
            <h1>What Else Can <span>Bella</span> Do For You?</h1>
            <p>Everything you need to convert more leads—without hiring more staff.</p>
        </div>
        
        <div class="ai-features-grid">
            <!-- Speed to Lead -->
            <div class="ai-feature-card">
                <div class="ai-icon-container">
                    <div class="ai-feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                </div>
                <h2>Speed to Lead That Actually Converts</h2>
                <p>Every second counts. We respond to your leads within moments—while they're still interested, engaged, and ready to act. Our <span class="ai-highlight">data shows</span> businesses using Bella's speed-to-lead feature see conversion rates increase by up to 78%.</p>
                <div class="ai-tagline">Fast Follow-Up = More Sales</div>
            </div>
            
            <!-- Voice AI -->
            <div class="ai-feature-card">
                <div class="ai-icon-container">
                    <div class="ai-feature-icon">
                        <i class="fas fa-phone-alt"></i>
                    </div>
                </div>
                <h2>Voice Calls That Feel Human</h2>
                <p>Turn leads into conversations with natural-sounding AI voice calls that connect in seconds. No hold music. No awkward bots. Just smooth, <span class="ai-highlight">human-like calls</span> that qualify and convert.</p>
                <div class="ai-tagline">Scalable Conversations. Real Results</div>
            </div>
            
            <!-- Review -->
            <div class="ai-feature-card">
                <div class="ai-icon-container">
                    <div class="ai-feature-icon">
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                <h2>Automate Your Reviews</h2>
                <p>Bella follows up with happy customers at the perfect moment—so you get more <span class="ai-highlight">5-star reviews</span> without lifting a finger. Our automated review system captures feedback when satisfaction is highest.</p>
                <div class="ai-tagline">Build Trust. Boost Rankings.</div>
            </div>
            
            <!-- WhatsApp -->
            <div class="ai-feature-card">
                <div class="ai-icon-container">
                    <div class="ai-feature-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                </div>
                <h2>WhatsApp Engagement</h2>
                <p>Bella connects with your leads through their preferred messaging platform. Our WhatsApp integration delivers <span class="ai-highlight">instant responses</span>, personalized follow-ups, and automated nurturing—all with 98% open rates.</p>
                <div class="ai-tagline">Global Reach. Instant Connection.</div>
            </div>
        </div>
    </div>
</div></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div id="button-XOKFNf57PF" class="c-button c-wrapper button-XOKFNf57PF"><!----><!----><!----><!----><!----><!----><!--[--><!----><!----><button data-animation-class="animate__animated animate__fadeInDown" id="button-XOKFNf57PF_btn" style="" class="cbutton-XOKFNf57PF button-shadow2 custom btn-vp btn-hp borderFull radius75 none" aria-label="Book A Discovery Call "><div style="" class="main-heading-group"><div class="button-icon-start"></div><div class="main-heading-button">Book A Discovery Call</div><div class="button-icon-end"></div></div><!----><div style="display:none;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);" class="btn-loader-position"><div style="display:none;" class="v-spinner"><div class="v-moon v-moon1" style="height:30px;width:30px;border-radius:100%;"><div class="v-moon v-moon2" style="height:4.285714285714286px;width:4.285714285714286px;border-radius:100%;top:12.857142857142858px;background-color:rgb(255, 255, 255);"></div><div class="v-moon v-moon3" style="height:30px;width:30px;border-radius:100%;border:4.285714285714286px solid rgb(255, 255, 255);"></div></div></div></div></button><div><!----></div><!----><!----><!--]--><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="about-us fullSection none noBorder radius0 none c-section c-wrapper section-ay3GWFejuN" style="" id="section-ay3GWFejuN"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="row-align-center noBorder radius0 none c-row c-wrapper row-fXS5rW5fqi" id="row-fXS5rW5fqi"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="noBorder radius0 none c-column c-wrapper col-4Uk1NVJdvV" id="col-4Uk1NVJdvV"><!----><!----><div class="vertical inner"><!----><!--[--><!--[--><div id="custom-code-IrpQK2lftA" class="c-custom-code c-wrapper custom-code-IrpQK2lftA"><!----><!----><!----><!----><!----><!----><!----><span></span><div id="custom-code-IrpQK2lftA" class="custom-code-container ccustom-code-IrpQK2lftA"><script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');
    
    :root {
        --db-calc-primary-color: #3b82f6;
        --db-calc-secondary-color: #555;
        --db-calc-accent-color: #1e40af;
        --db-calc-success-color: #10b981;
        --db-calc-light-color: #f8fafc;
        --db-calc-dark-color: #1e293b;
    }
    
    .db-calc-wrapper * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: 'Poppins', sans-serif;
    }
    
    .db-calc-body {
        background-color: #fff;
        color: var(--db-calc-dark-color);
        line-height: 1.6;
    }
    
    .db-calc-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .db-calc-header {
        text-align: center;
        margin-bottom: 40px;
    }
    
    .db-calc-header h1 {
        color: var(--db-calc-dark-color);
        margin-bottom: 15px;
        font-size: 3rem;
        font-weight: 700;
        letter-spacing: -0.03em;
        line-height: 1.2;
        text-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    
    .db-calc-header p {
        color: var(--db-calc-secondary-color);
        font-size: 1.1rem;
        font-family: 'Montserrat', sans-serif;
        font-weight: 500;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .db-calc-calculator-section {
        background-color: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(59, 130, 246, 0.1);
        animation: db-calc-fadeIn 0.3s;
        border-top: 4px solid var(--db-calc-primary-color);
        position: relative;
        overflow: hidden;
    }

    .db-calc-calculator-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 90% 10%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
        pointer-events: none;
        z-index: 1;
    }

    .db-calc-calculator-section > * {
        position: relative;
        z-index: 2;
    }
    
    @keyframes db-calc-fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    .db-calc-form-group {
        margin-bottom: 20px;
    }
    
    .db-calc-form-group label {
        display: block;
        font-weight: 600;
        margin-bottom: 8px;
        color: var(--db-calc-dark-color);
    }
    
    .db-calc-form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 16px;
        transition: border-color 0.15s ease-in-out;
    }
    
    .db-calc-form-control:focus {
        border-color: var(--db-calc-primary-color);
        outline: none;
    }
    
    .db-calc-btn {
        padding: 12px 25px;
        background: linear-gradient(90deg, var(--db-calc-primary-color), var(--db-calc-accent-color));
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .db-calc-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
    }
    
    .db-calc-result-section {
        margin-top: 30px;
        background-color: #f8f9ff;
        padding: 20px;
        border-radius: 6px;
        display: none;
    }
    
    .db-calc-result-section h3 {
        color: var(--db-calc-dark-color);
        margin-bottom: 15px;
    }
    
    .db-calc-result-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 10px;
        background-color: white;
        border-radius: 4px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .db-calc-result-item .db-calc-label {
        font-weight: 600;
    }
    
    .db-calc-result-item .db-calc-value {
        color: var(--db-calc-primary-color);
        font-weight: 700;
        position: relative;
        display: inline-block;
    }
    
    .db-calc-result-item .db-calc-value::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -3px;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, var(--db-calc-primary-color), var(--db-calc-accent-color));
        border-radius: 2px;
        opacity: 0.6;
    }
    
    .db-calc-chart-container {
        margin-top: 30px;
        height: 350px;
    }
    
    .db-calc-calculation-notes {
        margin-top: 30px;
        background-color: #f0f2ff;
        padding: 15px;
        border-radius: 6px;
        font-size: 14px;
        color: var(--db-calc-secondary-color);
    }
    
    .db-calc-calculation-notes h4 {
        margin-bottom: 10px;
        color: var(--db-calc-dark-color);
    }
    
    .db-calc-currency-row {
        display: flex;
        gap: 15px;
    }
    
    .db-calc-currency-row .db-calc-form-group {
        flex: 1;
    }
    
    @media (max-width: 768px) {
        .db-calc-calculator-section {
            padding: 20px;
        }
        
        .db-calc-header h1 {
            font-size: 2.5rem;
        }
        
        .db-calc-header p {
            font-size: 1rem;
        }
        
        .db-calc-currency-row {
            flex-direction: column;
            gap: 0;
        }
    }
    
    @media (max-width: 480px) {
        .db-calc-header h1 {
            font-size: 2rem;
        }
    }
</style>

<div class="db-calc-wrapper">
    <div class="db-calc-container">
        <div class="db-calc-header">
            <h1>Are You Currently Sitting On A<br><span style="color: var(--db-calc-primary-color); font-weight: 800; position: relative; display: inline-block;">Gold Mine?<span style="position: absolute; left: 0; bottom: -3px; width: 100%; height: 3px; background: linear-gradient(90deg, #f97150, #f08a28); border-radius: 2px;"></span></span></h1>
            <p>Estimate potential revenue from reviving unconverted leads in your database</p>
        </div>
        
        <div class="db-calc-calculator-section">
            <h2 style="font-family: 'Poppins', sans-serif; font-weight: 700; color: var(--db-calc-dark-color); margin-bottom: 15px;">Calculate Potential Revenue</h2>
            <p style="font-family: 'Montserrat', sans-serif; color: var(--db-calc-secondary-color); margin-bottom: 25px;">See how much revenue you could generate by reviving old leads in your database</p>
            
            <div class="db-calc-form-group">
                <label for="db-calc-old-leads">Number of Unconverted Leads in Database</label>
                <input type="number" id="db-calc-old-leads" class="db-calc-form-control" placeholder="e.g. 5000">
            </div>
            
            <div class="db-calc-currency-row">
                <div class="db-calc-form-group">
                    <label for="db-calc-avg-sale-value">Average Sale Value</label>
                    <input type="number" id="db-calc-avg-sale-value" class="db-calc-form-control" placeholder="e.g. 1000">
                </div>
                
                <div class="db-calc-form-group">
                    <label for="db-calc-currency">Currency</label>
                    <select id="db-calc-currency" class="db-calc-form-control">
                        <option value="USD">USD ($)</option>
                        <option value="GBP">GBP (£)</option>
                        <option value="EUR">EUR (€)</option>
                    </select>
                </div>
            </div>
            
            <button id="db-calc-calculate-reactivation" class="db-calc-btn">Calculate Potential Revenue</button>
            
            <div id="db-calc-reactivation-results" class="db-calc-result-section">
                <h3>Potential Revenue from Database Revival</h3>
                
                <div class="db-calc-result-item">
                    <div class="db-calc-label">Total Unconverted Leads:</div>
                    <div class="db-calc-value" id="db-calc-total-leads">0</div>
                </div>
                
                <div class="db-calc-result-item">
                    <div class="db-calc-label">Average Sale Value:</div>
                    <div class="db-calc-value" id="db-calc-avg-sale">$0</div>
                </div>
                
                <div class="db-calc-chart-container">
                    <canvas id="db-calc-reactivation-chart"></canvas>
                </div>
                
                <div class="db-calc-calculation-notes">
                    <h4>How this is calculated:</h4>
                    <p>This calculator shows the potential revenue you could generate by converting different percentages of your unconverted leads. The industry average for successful database revival campaigns ranges from 1-8%, depending on the age of the leads, industry, and approach.</p>
                    <p>Each bar represents the potential revenue if you were able to convert that percentage of your previously unconverted leads in your database.</p>
                    <p>For example, if you have 10,000 unconverted leads and an average sale value of 1,000:</p>
                    <ul id="db-calc-example-list">
                        <li>At 1% conversion: 100 leads × $1,000 = $100,000 revenue</li>
                        <li>At 5% conversion: 500 leads × $1,000 = $500,000 revenue</li>
                        <li>At 8% conversion: 800 leads × $1,000 = $800,000 revenue</li>
                    </ul>
                    <p>Most companies can expect conversion rates in the 1-3% range for a revival campaign, with top-performing campaigns reaching 5-8% for warm leads.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Database Reactivation Calculator
    const dbCalcBtn = document.getElementById('db-calc-calculate-reactivation');
    
    // Currency symbols mapping
    const currencySymbols = {
        'USD': '$',
        'GBP': '£',
        'EUR': '€'
    };
    
    dbCalcBtn.addEventListener('click', () => {
        const oldLeads = parseInt(document.getElementById('db-calc-old-leads').value) || 0;
        const avgSaleValue = parseFloat(document.getElementById('db-calc-avg-sale-value').value) || 0;
        const currencySelect = document.getElementById('db-calc-currency');
        const currency = currencySelect.value;
        const currencySymbol = currencySymbols[currency];
        
        if (oldLeads === 0 || avgSaleValue === 0) {
            alert('Please enter values for both fields');
            return;
        }
        
        // Display the results section
        document.getElementById('db-calc-reactivation-results').style.display = 'block';
        
        // Update result values
        document.getElementById('db-calc-total-leads').textContent = oldLeads.toLocaleString();
        document.getElementById('db-calc-avg-sale').textContent = `${currencySymbol}${avgSaleValue.toLocaleString()}`;
        
        // Update example list with correct currency
        const exampleList = document.getElementById('db-calc-example-list');
        exampleList.innerHTML = `
            <li>At 1% conversion: 100 leads × ${currencySymbol}1,000 = ${currencySymbol}100,000 revenue</li>
            <li>At 5% conversion: 500 leads × ${currencySymbol}1,000 = ${currencySymbol}500,000 revenue</li>
            <li>At 8% conversion: 800 leads × ${currencySymbol}1,000 = ${currencySymbol}800,000 revenue</li>
        `;
        
        // Generate data for the chart
        const conversionRates = [1, 2, 3, 4, 5, 6, 7, 8]; // 1% to 8%
        const data = conversionRates.map(rate => {
            const convertedLeads = Math.round(oldLeads * (rate / 100));
            const revenue = convertedLeads * avgSaleValue;
            return {
                conversionRate: rate,
                revenue: revenue,
                convertedLeads: convertedLeads
            };
        });
        
        // Create the chart
        const ctx = document.getElementById('db-calc-reactivation-chart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (window.dbCalcReactivationChart) {
            window.dbCalcReactivationChart.destroy();
        }
        
        window.dbCalcReactivationChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: conversionRates.map(rate => `${rate}%`),
                datasets: [{
                    label: `Potential Revenue (${currencySymbol})`,
                    data: data.map(d => d.revenue),
                    backgroundColor: function(context) {
                        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
                        gradient.addColorStop(0, 'rgba(249, 113, 80, 0.8)');
                        gradient.addColorStop(1, 'rgba(240, 138, 40, 0.7)');
                        return gradient;
                    },
                    borderColor: 'rgba(249, 113, 80, 1)',
                    borderWidth: 1,
                    borderRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                return `Potential Revenue: ${currencySymbol}${value.toLocaleString()}`;
                            },
                            afterLabel: function(context) {
                                const index = context.dataIndex;
                                return `Converted Leads: ${data[index].convertedLeads}`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return currencySymbol + value.toLocaleString();
                            }
                        },
                        title: {
                            display: true,
                            text: `Potential Revenue (${currencySymbol})`
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Conversion Rate'
                        }
                    }
                }
            }
        });
    });
</script></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div id="button-cX3vuxtbMw" class="c-button c-wrapper button-cX3vuxtbMw"><!----><!----><!----><!----><!----><!----><!--[--><!----><!----><button data-animation-class="animate__animated animate__fadeInDown" id="button-cX3vuxtbMw_btn" style="" class="cbutton-cX3vuxtbMw button-shadow2 custom btn-vp btn-hp borderFull radius75 none" aria-label="Book A Discovery Call "><div style="" class="main-heading-group"><div class="button-icon-start"></div><div class="main-heading-button">Book A Discovery Call</div><div class="button-icon-end"></div></div><!----><div style="display:none;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);" class="btn-loader-position"><div style="display:none;" class="v-spinner"><div class="v-moon v-moon1" style="height:30px;width:30px;border-radius:100%;"><div class="v-moon v-moon2" style="height:4.285714285714286px;width:4.285714285714286px;border-radius:100%;top:12.857142857142858px;background-color:rgb(255, 255, 255);"></div><div class="v-moon v-moon3" style="height:30px;width:30px;border-radius:100%;border:4.285714285714286px solid rgb(255, 255, 255);"></div></div></div></div></button><div><!----></div><!----><!----><!--]--><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="fullSection noBorder radius0 none c-section c-wrapper section-ki-126gO-0" style="" id="section-ki-126gO-0"><!--[--><!----><div class="bg bgCover bg-section-ki-126gO-0 none" style="border-radius:-2px;position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;"></div><!--]--><!----><div class="inner"><!----><!--[--><!--[--><div class="row-align-center noBorder radius0 none c-row c-wrapper row-zSC_XVvBwG" id="row-zSC_XVvBwG"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="noBorder radius0 none c-column c-wrapper col-3YGUrwgtwd" id="col-3YGUrwgtwd"><!----><!----><div class="vertical inner"><!----><!--[--><!--[--><div id="custom-code-KFlAqIVW_K" class="c-custom-code c-wrapper custom-code-KFlAqIVW_K"><!----><!----><!----><!----><!----><!----><!----><span></span><div id="custom-code-KFlAqIVW_K" class="custom-code-container ccustom-code-KFlAqIVW_K"><style>
        /* Import Google Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');
        
        /* CTA Section with unique prefix */
        .ai-cta-section {
            font-family: 'Poppins', sans-serif;
            max-width: 1200px;
            margin: 60px auto 40px;
            padding: 0 20px;
        }
        
        .ai-cta-header {
            text-align: center;
            margin-bottom: 40px;
            background-color: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .ai-cta-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #222;
            margin-bottom: 15px;
            line-height: 1.2;
            letter-spacing: -0.03em;
            position: relative;
            display: inline-block;
        }
        
        .ai-cta-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #3b82f6, #1e40af);
            border-radius: 2px;
        }
        
        .ai-cta-subtitle {
            font-family: 'Montserrat', sans-serif;
            font-size: 1.1rem;
            max-width: 700px;
            margin: 1.5rem auto 0;
            color: #333;
            line-height: 1.6;
        }
        
        .ai-cta-highlight {
            color: #3b82f6;
            font-weight: 700;
        }

        .ai-calendly-container {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.15);
            padding: 30px;
            border: 2px solid #3b82f6;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }

        .ai-calendly-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }

        .ai-calendly-container > * {
            position: relative;
            z-index: 2;
        }
        
        @media (max-width: 768px) {
            .ai-cta-title {
                font-size: 1.8rem;
            }
            
            .ai-cta-subtitle {
                font-size: 0.95rem;
            }
            
            .ai-calendly-container {
                padding: 20px;
            }
            
            .ai-cta-header {
                padding: 20px;
            }
        }
        
        @media (max-width: 480px) {
            .ai-cta-title {
                font-size: 1.6rem;
            }
            
            .ai-cta-subtitle {
                font-size: 0.9rem;
            }
            
            .ai-cta-section {
                padding: 0 10px;
            }
        }
</style>

<div class="ai-cta-section">
    <div class="ai-cta-header">
        <h2 class="ai-cta-title">Schedule Your <span class="ai-cta-highlight">Discovery Call</span></h2>
        <p class="ai-cta-subtitle">Get your personalized AI lead follow-up demo and see exactly how Bella can boost your conversion rates.</p>
    </div>
    
    <div class="ai-calendly-container">
        <!-- Calendly inline widget begin -->
        <div class="calendly-inline-widget" data-url="https://calendly.com/nextlevelgrowthpartners/30min" style="min-width:320px;height:700px;"></div>
        <script type="text/javascript" src="https://assets.calendly.com/assets/external/widget.js" async></script>
        <!-- Calendly inline widget end -->
    </div>
</div></div><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="pb-footer fullSection none noBorder radius0 none c-section c-wrapper section-kr3hxqv4nG" style="" id="section-kr3hxqv4nG"><!--[--><!----><div class="bg bgCover bg-section-kr3hxqv4nG none" style="border-radius:-2px;position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;"></div><!--]--><!----><div class="inner"><!----><!--[--><!--[--><div class="row-align-center none noBorder radius0 none c-row c-wrapper row-Jpvu0Q0cfHn" id="row-Jpvu0Q0cfHn"><!----><!----><div class="inner"><!----><!--[--><!--[--><div class="c-column c-wrapper col-hC6omOqyxcy" id="col-hC6omOqyxcy"><!----><!----><div class="none noBorder radius0 none bg bgCover vertical inner"><!----><!--[--><!--[--><div id="image-sH5K7FFn_vn" class="c-image c-wrapper image-sH5K7FFn_vn"><!----><!----><!----><!----><!----><div style="cursor:pointer;" class="image-container cimage-sH5K7FFn_vn"><div><div><picture class="hl-image-picture" style="display:block;"><source media="(max-width:900px) and (min-width: 768px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_900/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><source media="(max-width:768px) and (min-width: 640px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_768/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><source media="(max-width:640px) and (min-width: 480px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_640/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><source media="(max-width:480px) and (min-width: 320px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_480/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><source media="(max-width:320px)" srcset="https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png"><img src="https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://assets.cdn.filesafe.space/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png" alt="" style="" class="img-none img-border-none img-shadow-none img-effects-none hl-optimized mw-100" loading="lazy" data-animation-class=""></picture></div></div></div><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="c-column c-wrapper col-xfeMEp_LXBJ" id="col-xfeMEp_LXBJ"><!----><!----><div class="none noBorder radius0 none bg bgCover vertical inner"><!----><!--[--><!--[--><div id="paragraph-7jGhmntDzb" class="c-paragraph c-wrapper"><!----><!----><!----><div class="paragraph-7jGhmntDzb text-output cparagraph-7jGhmntDzb noBorder radius0 none" data-animation-class><div><p>Contacts:</p><p></p><p>************</p><p><a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="0f7b60624f616a777b636a796a63687d60787b677f6e7d7b616a7d216c6062">[email&#160;protected]</a></p><p></p><p>************</p><p><a href="/cdn-cgi/l/email-protection" class="__cf_email__" data-cfemail="6805091a010728060d101c040d1e0d040f1a071f1c0018091a1c060d1a460b0705">[email&#160;protected]</a></p><p></p><p><a target="_blank" rel="noopener noreferrer nofollow" href="https://nextlevelgrowthpartner.com/privacypolicy">Terms of Service</a> | <a target="_blank" rel="noopener noreferrer nofollow" href="https://nextlevelgrowthpartner.com/privacypolicy">Privacy Policy</a></p></div></div><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--[--><div class="c-column c-wrapper col-PiXgaEIcg0E" id="col-PiXgaEIcg0E"><!----><!----><div class="none noBorder radius0 none bg bgCover horizontal inner"><!----><!--[--><!--[--><div id="button-2MeJJFeA3Dd" class="c-button c-wrapper button-2MeJJFeA3Dd"><!----><!----><!----><!----><!----><!----><!--[--><a id="button-2MeJJFeA3Dd_btn" href="https://www.instagram.com/nextlevelgrowthpartners/?hl=en" target="_blank" rel="noreferrer noopener" data-animation-class="" class="cbutton-2MeJJFeA3Dd buttonElevate btnshadow custom btn-vp btn-hp borderFull radius20 none text-center" aria-label="https://www.instagram.com/nextlevelgrowthpartners/?hl=en"><span style="" class="main-heading-group"><span style="margin-right:5px;" class="button-icon-start"></span><span class="main-heading-button"></span><span style="margin-left:5px;" class="button-icon-end"></span></span><!----></a><!----><!----><div><!----></div><!----><!----><!--]--><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div><!----><!----><!----><!----><span></span><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----><!----></div><!--]--><!--]--></div></div><!----><!----></div><!--]--></div><!--]--><!--]--><!--]--><!--]--></div><div id="teleports"></div><script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script type="application/json" data-nuxt-data="nuxt-app" data-ssr="true" id="__NUXT_DATA__">[["ShallowReactive",1],{"data":2,"state":1331,"once":1466,"_errors":1467,"serverRendered":136},["ShallowReactive",3],{"pageData":4},{"elements":5,"popup":1201,"fontsToLoad":1313,"meta":1314,"domainName":1321,"pageUrl":1322,"pageId":1323,"pageName":1324,"locationId":1325,"headerCode":49,"footerCode":49,"favicon":1326,"globalHeadTrackingCode":49,"globalBodyTrackingCode":49,"funnelId":1327,"funnelName":1328,"stepId":1329,"affiliateId":-1,"cookieConsent":-1,"disablePageLevelCookieConsent":55,"pixelToInit":1330,"isOptimisePageLoad":136,"backgroundSettingsClass":60},[6,15,68,104,144,238,272,312,347,378,413,426,459,470,506,545,556,589,622,660,671,700,730,763,801,830,841,878,911,948,959,993,1025,1059,1093,1128,1144,1177],{"id":7,"child":8},"hl_main",[9,10,11,12,13,14],"section-m0AmIUbB7","section-wAGehRwoGr","section-sfVzbZHQjy","section-ay3GWFejuN","section-ki-126gO-0","section-kr3hxqv4nG",{"id":9,"type":16,"child":17,"class":19,"styles":28,"extra":50,"wrapper":65,"meta":16,"tagName":66,"title":67,"_id":9},"section",[18],"row-xmdyLrDPJd",{"width":20,"borders":22,"borderRadius":24,"radiusEdge":26},{"value":21},"fullSection",{"value":23},"noBorder",{"value":25},"radius0",{"value":27},"none",{"paddingLeft":29,"paddingRight":32,"paddingBottom":34,"paddingTop":36,"marginTop":37,"marginBottom":38,"backgroundColor":39,"borderColor":41,"borderWidth":43,"borderStyle":45,"marginRight":47,"boxShadow":48},{"unit":30,"value":31},"px",0,{"value":33,"unit":30},"0",{"unit":30,"value":35},"30",{"unit":30,"value":35},{"unit":30,"value":31},{"unit":30,"value":31},{"value":40},"var(--transparent)",{"value":42},"var(--black)",{"value":44,"unit":30},"2",{"value":46},"solid",{"unit":30,"value":31},{"value":49},"",{"sticky":51,"visibility":53,"bgImage":56,"allowRowMaxWidth":62,"customClass":63},{"value":52},"noneSticky",{"value":54},{"hideDesktop":55,"hideMobile":55},false,{"value":57},{"url":58,"opacity":59,"options":60,"svgCode":49,"servingUrl":49,"placeholderBase64":49,"imageMeta":49,"mediaType":61,"showSvgToggle":55,"videoUrl":49},"https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3","1","bgCover","image",{"value":55},{"value":64},[],{},"c-section","Header",{"id":18,"type":69,"child":70,"class":73,"styles":80,"extra":89,"wrapper":99,"tagName":102,"meta":69,"title":103},"row",[71,72],"col-qVAwIKxF0h","col-rUj1LnNf1n",{"alignRow":74,"boxShadow":76,"borders":77,"borderRadius":78,"radiusEdge":79},{"value":75},"row-align-center",{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":81,"paddingRight":82,"paddingTop":83,"paddingBottom":84,"backgroundColor":85,"borderColor":86,"borderWidth":87,"borderStyle":88},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":33},{"unit":30,"value":33},{"value":40},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":90,"bgImage":92,"rowWidth":94,"customClass":97},{"value":91},{"hideDesktop":55,"hideMobile":55},{"value":93},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":95,"unit":96},"100","%",{"value":98},[],{"marginTop":100,"marginBottom":101},{"unit":30,"value":31},{"unit":30,"value":31},"c-row","2 Column Row",{"id":71,"type":105,"child":106,"class":108,"styles":113,"extra":124,"wrapper":139,"tagName":142,"meta":105,"title":143},"col",[107],"nav-menu-ZXRIR7fv2T",{"boxShadow":109,"borders":110,"borderRadius":111,"radiusEdge":112},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":114,"paddingRight":115,"paddingTop":116,"paddingBottom":117,"backgroundColor":118,"width":119,"borderColor":121,"borderWidth":122,"borderStyle":123},{"unit":30,"value":33},{"value":33,"unit":30},{"unit":30,"value":33},{"unit":30,"value":33},{"value":40},{"value":120,"unit":96},70.4,{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":125,"bgImage":127,"columnLayout":129,"justifyContentColumnLayout":131,"alignContentColumnLayout":133,"forceColumnLayoutForMobile":135,"customClass":137},{"value":126},{"hideDesktop":55,"hideMobile":55},{"value":128},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":130},"column",{"value":132},"center",{"value":134},"inherit",{"value":136},true,{"value":138},[],{"marginTop":140,"marginBottom":141},{"unit":30,"value":31},{"unit":30,"value":31},"c-column","1st Column",{"extra":145,"id":107,"meta":197,"tagName":198,"class":199,"styles":203},{"nodeId":146,"visibility":147,"menuItems":149,"mobileFontSize":167,"desktopFontSize":169,"typography":171,"icon":173,"text":178,"imageProperties":180,"includeLogoInMenu":186,"imageActions":187,"visitWebsite":188,"includeHeadlineInMenu":191,"menuLayout":192,"customClass":194},"cnav-menu-ZXRIR7fv2T",{"value":148},{"hideDesktop":49,"hideMobile":49},{"value":150},[151,157,162],{"id":152,"title":153,"goTo":154,"url":155,"goToId":49,"openInNewTab":55,"childs":156},"menu-item-TBWs9przI4","Our Services","url","#section-sfVzbZHQjy",[],{"id":158,"title":159,"goTo":154,"url":160,"goToId":49,"openInNewTab":55,"childs":161},"menu-item-6Hu6gkmNKd","Revenue Calculator","#section-ay3GWFejuN",[],{"id":163,"title":164,"goTo":154,"url":165,"goToId":49,"openInNewTab":55,"childs":166},"menu-item-6jJn2rzFzZ","Contact Us","#section-G_AZ8TUXTY",[],{"value":168,"unit":30},"20",{"value":170,"unit":30},"18",{"value":172},"var(--poppins)",{"value":174},{"name":175,"unicode":176,"fontFamily":177,"color":42},"bars","f0c9","Font Awesome 5 Free",{"value":179},"\u003Cp>\u003Cstrong>Business Name\u003C/strong>\u003C/p>",{"value":181},{"width":182,"height":183,"url":184,"altText":185,"compression":136,"placeholderBase64":49,"servingUrl":49,"imageMeta":49},"230px",null,"https://storage.googleapis.com/msgsndr/ueNjo8LgFsCPSK7POBiq/media/67fbed473baf5da0d8866d24.png","Brand Logo",{"value":136},{"value":27},{"value":189},{"url":190,"newTab":55},"#section-WMZMMQViW2",{"value":55},{"value":193},"default",{"value":195},[196],"pb-menu","nav-menu","c-nav-menu",{"borders":200,"borderRadius":201,"radiusEdge":202},{"value":23},{"value":25},{"value":27},{"paddingTop":204,"paddingBottom":205,"paddingLeft":206,"paddingRight":207,"marginTop":208,"backgroundColor":209,"mobileBackgroundColor":210,"color":212,"boldTextColor":214,"italicTextColor":215,"underlineTextColor":216,"iconColor":217,"secondaryColor":218,"navMenuItemHoverBackgroundColor":219,"lineHeight":220,"textTransform":223,"letterSpacing":224,"textAlign":225,"borderColor":227,"borderWidth":228,"borderStyle":229,"navMenuItemSpacingX":230,"navMenuItemSpacingY":231,"dropdownBackground":233,"dropdownTextColor":234,"dropdownHoverColor":235,"dropdownItemSpacing":236},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":31},{"value":40},{"value":211},"var(--overlay)",{"value":213},"var(--white)",{"value":213},{"value":213},{"value":213},{"value":213},{"value":213},{"value":40},{"value":221,"unit":222},1.3,"em",{"value":27},{"value":33,"unit":30},{"value":226},"left",{"value":42},{"value":44,"unit":30},{"value":46},{"value":168,"unit":30},{"value":232,"unit":30},"5",{"value":213},{"value":42},{"value":42},{"value":237,"unit":30},10,{"id":72,"type":105,"child":239,"class":241,"styles":246,"extra":257,"wrapper":268,"tagName":142,"meta":105,"title":271},[240],"button-dDRV8gIyYx",{"boxShadow":242,"borders":243,"borderRadius":244,"radiusEdge":245},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":247,"paddingRight":248,"paddingTop":249,"paddingBottom":250,"backgroundColor":251,"width":252,"borderColor":254,"borderWidth":255,"borderStyle":256},{"unit":30,"value":33},{"value":33,"unit":30},{"unit":30,"value":33},{"unit":30,"value":31},{"value":40},{"value":253,"unit":96},29.6,{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":258,"bgImage":260,"columnLayout":262,"justifyContentColumnLayout":263,"alignContentColumnLayout":264,"forceColumnLayoutForMobile":265,"customClass":266},{"value":259},{"hideDesktop":55,"hideMobile":136},{"value":261},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":130},{"value":132},{"value":134},{"value":136},{"value":267},[],{"marginTop":269,"marginBottom":270},{"unit":30,"value":31},{"unit":30,"value":31},"2nd Column",{"extra":273,"id":240,"tagName":294,"meta":295,"class":296},{"visibility":274,"text":276,"subText":278,"productId":279,"action":281,"visitWebsite":283,"hideElements":284,"showElements":285,"scrollToElement":286,"stepPath":287,"saleAction":288,"phoneNumber":289,"emailAddress":290,"customClass":291,"nodeId":293},{"value":275},{"hideMobile":49,"hideDesktop":49},{"value":277},"Book A Discovery Call",{},{"value":280},{},{"value":282},"scroll-to-element",{},{},{},{"value":13},{},{},{},{},{"value":292},[],"cbutton-dDRV8gIyYx","c-button","button",{"buttonBoxShadow":297,"buttonBgStyle":299,"buttonVp":301,"buttonHp":303,"borders":305,"borderRadius":307,"radiusEdge":309,"entranceAnimation":310},{"value":298},"button-shadow2",{"value":300},"custom",{"value":302},"btn-vp",{"value":304},"btn-hp",{"value":306},"borderFull",{"value":308},"radius75",{"value":27},{"value":311},"animate__animated animate__fadeInDown",{"id":10,"type":16,"child":313,"class":316,"styles":322,"extra":335,"wrapper":345,"meta":16,"tagName":66,"title":346,"_id":10},[314,315],"row-2tWJqvlq4x","row-N6rakETuRp",{"width":317,"boxShadow":318,"borders":319,"borderRadius":320,"radiusEdge":321},{"value":21},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":323,"paddingRight":324,"paddingBottom":325,"paddingTop":327,"marginTop":328,"marginBottom":329,"backgroundColor":330,"borderColor":332,"borderWidth":333,"borderStyle":334},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":326},"118",{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{"value":331},"var(--color-lkbjs320)",{"value":42},{"value":44,"unit":30},{"value":46},{"sticky":336,"visibility":337,"bgImage":339,"allowRowMaxWidth":341,"customClass":342},{"value":52},{"value":338},{"hideDesktop":55,"hideMobile":55},{"value":340},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":55},{"value":343},[344],"about-us",{},"Section",{"id":315,"type":69,"child":348,"class":351,"styles":357,"extra":366,"wrapper":375,"tagName":102,"meta":69,"title":103},[349,350],"col-eosBjsQX8W","col-V1zVpPfYjM",{"alignRow":352,"boxShadow":353,"borders":354,"borderRadius":355,"radiusEdge":356},{"value":75},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":358,"paddingRight":359,"paddingTop":360,"paddingBottom":361,"backgroundColor":362,"borderColor":363,"borderWidth":364,"borderStyle":365},{"unit":30,"value":33},{"value":33,"unit":30},{"unit":30,"value":33},{"unit":30,"value":33},{"value":40},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":367,"bgImage":369,"rowWidth":371,"customClass":373},{"value":368},{"hideDesktop":55,"hideMobile":55},{"value":370},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":372,"unit":96},100,{"value":374},[],{"marginTop":376,"marginBottom":377},{"unit":30,"value":31},{"unit":30,"value":31},{"id":350,"type":105,"child":379,"class":381,"styles":387,"extra":397,"wrapper":409,"tagName":142,"meta":105,"title":271},[380],"custom-code-F-9ZM6pPpK",{"boxShadow":382,"borders":383,"borderRadius":384,"radiusEdge":386},{"value":27},{"value":306},{"value":385},"radius50",{"value":27},{"paddingLeft":388,"paddingRight":389,"paddingTop":390,"paddingBottom":391,"backgroundColor":392,"width":393,"borderColor":394,"borderWidth":395,"borderStyle":396},{"unit":30,"value":33},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":168},{"value":40},{"value":95,"unit":96},{"value":40},{"value":59,"unit":30},{"value":46},{"visibility":398,"bgImage":400,"columnLayout":403,"justifyContentColumnLayout":404,"alignContentColumnLayout":405,"forceColumnLayoutForMobile":406,"customClass":407},{"value":399},{"hideDesktop":55,"hideMobile":55},{"value":401},{"url":49,"opacity":59,"options":402,"svgCode":49,"servingUrl":49,"placeholderBase64":49,"imageMeta":49},"bgNoRepeat",{"value":130},{"value":132},{"value":134},{"value":136},{"value":408},[],{"marginTop":410,"marginBottom":411,"marginRight":412},{"unit":30,"value":33},{"unit":30,"value":33},{"unit":30,"value":31},{"extra":414,"id":380,"meta":423,"tagName":424,"class":425},{"visibility":415,"customCode":417,"customClass":420,"nodeId":422},{"value":416},{"hideMobile":49,"hideDesktop":49},{"value":418},{"rawCustomCode":419},"\u003Clink href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\" rel=\"stylesheet\">\n\u003Cscript src=\"https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js\">\u003C/script>\n\u003Cstyle>\n    @import url(\"https://fonts.googleapis.com/css2?family=Rubik:wght@400;500;700&display=swap\");\n\n    .info {\n        font-size: 1.3rem;\n        margin: 1rem;\n    }\n\n    .title {\n        font-size: 2rem;\n        margin: 2rem 0;\n    }\n\n    .info-text {\n        color: rgba(0, 0, 0, 0.575);\n        max-width: 25rem;\n        text-align: justify;\n    }\n\n    .container {\n        display: flex;\n        flex-direction: column; /* Changed to column to add notification text below */\n        justify-content: center;\n        align-items: center;\n    }\n\n    .phone-parent-container {\n        border: solid 0.5rem rgb(18, 18, 18);\n        border-radius: 40px;\n        box-shadow: 0 0 20px 0 rgba(255, 255, 255, 0.461);\n        height: 40rem;\n        width: 18.8rem;\n        background: hsl(0, 0%, 100%);\n        margin-top: 1rem;\n        position: relative; /* Added for glow effect positioning */\n        transition: box-shadow 0.5s ease; /* Smooth transition for glow effect */\n    }\n\n    /* Glow effects */\n    .glow-blue {\n        box-shadow: 0 0 25px 5px rgba(0, 123, 255, 0.7);\n    }\n\n    .glow-green {\n        box-shadow: 0 0 25px 5px rgba(40, 167, 69, 0.7);\n    }\n\n    .glow-yellow {\n        box-shadow: 0 0 25px 5px rgba(255, 193, 7, 0.7);\n    }\n\n    .glow-red {\n        box-shadow: 0 0 25px 5px rgba(255, 32, 7, 0.7);\n    }\n\n    .glow-purple {\n        box-shadow: 0 0 25px 5px rgba(255, 7, 243, 0.7);\n    }\n\n    /* Notification text below phone */\n    .notification-text {\n        margin-top: 1.5rem;\n        padding: 0.75rem 1.5rem;\n        border-radius: 10px;\n        font-family: \"Rubik\", sans-serif;\n        font-weight: 500;\n        background-color: #f8f9fa;\n        border-left: 5px solid #007bff;\n        box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n        opacity: 0;\n        transform: translateY(10px);\n        transition: opacity 0.5s ease, transform 0.5s ease;\n        max-width: 80%;\n        text-align: center;\n    }\n\n    .notification-visible {\n        opacity: 1;\n        transform: translateY(0);\n    }\n\n    /* Confetti container */\n    .confetti-container {\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        pointer-events: none;\n        z-index: 1000;\n    }\n\n    .phone-child-container {\n        border-radius: 0.9375rem;  \n        height: inherit;\n    }\n\n    .footer {\n        margin: 2rem 0;\n        text-align: center;\n    }\n\n    a {\n        color: #000;\n        text-decoration: none;\n    }\n\n    @media(max-width: 725px) {\n        .small-only {\n            display: block;\n        }\n\n        .container {\n            flex-direction: column;\n        }\n\n        .title {\n            text-align: center;\n        }\n    }\n\n    .phone-header {\n        background-color: rgba(233, 233, 235, 255);\n        border-radius: 40px 40px 0 0;\n        box-shadow: 0 0 0.625rem rgba(0, 0, 0, 0.1);\n        color: #000;\n        display: flex;\n        flex-direction: column; /* Organize top and bottom layers */\n        align-items: center;\n        justify-content: flex-start;\n        height: 5rem;\n        font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n        font-size: 0.9rem;\n        position: relative; /* For absolute positioning of camera module */\n        padding: 0 1rem;\n        border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* Thin black border at the bottom */\n    }\n\n    .phone-header-top {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        padding-top: 0.5rem;\n    }\n\n    .phone-header-time {\n        font-size: 0.75rem;\n        font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n        margin-left: 1rem;\n        margin-top: 0.2rem;\n        font-weight: bold; /* Makes the icons' text bold */\n    }\n\n    /* Camera Module */\n    .phone-header-camera {\n        background-color: #1b1a1a;\n        border-radius: 1rem;\n        height: 1.2rem;\n        width: 5rem;\n        position: absolute; /* Position independently */\n        top: 0.5rem; /* Adjust vertical alignment */\n        left: 50%; /* Center horizontally */\n        transform: translateX(-50%); /* Offset to truly center the module */\n        z-index: 1; /* Ensure it appears above other elements */\n    }\n\n    .phone-header-icons {\n        display: flex; /* Ensures the icons are in a row */\n        align-items: center; /* Aligns the icons vertically */\n        gap: 0.4rem; /* Adds space between the icons */\n    }\n\n    .phone-header-icons i {\n        font-size: 0.7rem; /* Adjust the size of the icons */\n        line-height: 1; /* Ensure consistent alignment */\n        color: #000; /* Optional: Set icon color */\n    }\n\n    .phone-header-bottom {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        width: 100%;\n        margin-top: 1rem; /* Space between top and bottom layers */\n    }\n\n    .back-button {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        width: 2rem;\n        height: 2rem;\n        background-color: transparent; /* Transparent background */\n        border-radius: 50%;\n        cursor: pointer;\n        color: #007bff; /* Blue color for the icon */\n        font-size: 1.2rem;\n    }\n\n    .back-button:hover {\n        background-color: rgba(0, 123, 255, 0.1); /* Subtle hover effect */\n    }\n\n    .back-button i {\n        font-size: 0.9rem; /* Adjust the size of the icons */\n        line-height: 1; /* Ensure consistent alignment */\n    }\n\n    .phone-header-name {\n        font-size: 1rem;\n        text-align: center;\n        flex: 1; /* Center the name between back and more info */\n    }\n\n    .video-button {\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        width: 2rem;\n        height: 2rem;\n        background-color: transparent; /* Transparent background */\n        border-radius: 50%;\n        cursor: pointer;\n        color: #007bff; /* Blue color for the icon */\n        font-size: 1.2rem;\n    }\n\n    .video-button:hover {\n        background-color: rgba(0, 123, 255, 0.1); /* Subtle hover effect */\n    }\n\n    .video-button i {\n        font-size: 0.9rem; /* Adjust the size of the icons */\n        line-height: 1; /* Ensure consistent alignment */\n    }\n\n    .phone-content {\n        height: 31rem; /* Ensure fixed height */\n        margin: 0 0 0 0;\n        overflow-y: auto; /* Enable vertical scrolling */\n        display: flex;\n        flex-direction: column; /* Keep messages in order */\n        gap: 0.5rem; /* Add space between messages */\n    }\n\n    /* For modern browsers like Chrome, Edge, Safari */\n    .phone-content::-webkit-scrollbar {\n        display: none; /* Hide scrollbar */\n    }\n\n    /* For Firefox */\n    .phone-content {\n        scrollbar-width: none; /* Hide scrollbar */\n    }\n\n    /* For Internet Explorer (just in case) */\n    .phone-content {\n        -ms-overflow-style: none; /* Hide scrollbar */\n    }\n\n    .msg {\n        border-radius: 16px; /* Rounded corners for bubble shape */\n        box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0); /* Subtle shadow */\n        font-size: 1rem;\n        font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif; /* Font stack */\n        margin-top: 0.4rem;\n        margin-bottom: 0.4rem;\n        padding: 0.5rem; /* Padding inside the bubble */\n        display: inline-block; /* Allow the width to adjust based on content */\n        max-width: 70%; /* Maximum width for longer messages */\n        word-wrap: break-word; /* Wrap text for longer messages */\n        position: relative;\n        opacity: 0;\n        transition: opacity 400ms ease, transform 400ms ease;\n    }\n\n    /* Received Messages */\n    .msg-received {\n        background-color: rgba(233, 233, 235, 255); /* iMessage gray */\n        color: #000000; /* Black text */\n        align-self: flex-start; /* Align to the left */\n        margin-left: 10px;\n        border-radius: 16px 16px 16px 0; /* Bubble shape for received messages */\n    }\n\n    /* Sent Messages */\n    .msg-sent {\n        background-color: rgba(0, 122, 255, 255); /* iMessage blue */\n        color: #ffffff; /* White text */\n        align-self: flex-end; /* Align to the right */\n        margin-right: 10px;\n        border-radius: 16px 16px 0 16px; /* Bubble shape for received messages */\n    }\n\n    /* Add smooth appearance for messages */\n    .show {\n        position: relative;\n        opacity: 1;\n        transform: translateY(0);\n    }\n\n    .phone-footer {\n        margin: 0.2rem;\n        justify-content: center;\n        align-items: center;\n        position: relative; /* Enables offset positioning */\n        top: 0rem; /* 0.2rem for local. 0rem for delpoyed */\n        border-radius: 1rem;\n    }\n\n    .footer-container {\n        position: relative;\n        background: none; /* Ensure no background is applied */\n        box-shadow: none; /* Remove any shadows if present */\n        border: none; /* Remove any borders if present */\n    }\n\n    .message-box {\n        display: flex; /* Flexbox to align text area and send button */\n        align-items: center; /* Center the button and text vertically */\n        border: 1px solid #ddd;\n        border-radius: 20px;\n        padding: 5px 10px; /* Add padding for better spacing */\n        background-color: rgba(240,240,240,255);\n        position: relative;\n        height: 2rem; /* Increase height for a single line message */\n    }\n\n    .enter-message {\n        flex: 1; /* Allow the text area to grow */\n        border: none; /* Remove border */\n        outline: none; /* Remove focus outline */\n        resize: none; /* Disable manual resizing */\n        font-family: \"Helvetica Neue\", Helvetica, Arial, sans-serif; /* Font stack */\n        font-size: 1rem;\n        /* border-radius: 20px; */\n        line-height: 1.5; /* Adjust line spacing for readability */\n        padding: 0.5px; /* Add padding inside the input box */\n        overflow: hidden; /* Prevent scrollbar */\n        height: 1.5rem; /* Match initial height for one line */\n        max-height: 8rem; /* Optional: Set a maximum height */\n        background: none;\n        text-align: left;\n    }\n\n    .send-button {\n        background-color: rgba(0, 122, 255, 1);\n        border: none;\n        border-radius: 1rem;\n        cursor: pointer;\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        height: 1.7rem;\n        width: 3rem;\n        margin-left: 10px;\n        color: white;\n        font-size: 1rem;\n        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;\n        text-align: center;\n        user-select: none;\n        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        transition: background-color 0.3s ease, transform 0.1s ease;\n    }\n\n\n    .send-button:hover {\n        background-color: #0056b3; /* Darker blue for hover */\n    }\n\n    .send-button:active {\n        background-color: #004085; /* Even darker blue for active state */\n        transform: scale(0.98); /* Add a slight \"press\" effect */\n    }\n\n    .send-button i {\n        margin: 0; /* Ensure icon is centered */\n    }\n\n\n    /* Loading Message (Styled as Received Message) */\n    .msg-received.loading {\n        display: flex; /* Flexbox for the dots */\n        justify-content: center; /* Center dots inside the bubble */\n        align-items: center; /* Vertically center dots */\n        background-color: rgba(233, 233, 235, 0.9); /* Slightly transparent gray */\n        max-width: 70%; /* Keep the same max width as received messages */\n        padding: 0.5rem; /* Consistent padding */\n        font-size: 0; /* Hide any unwanted text */\n    }\n\n    /* Dots for Typing Animation */\n    .msg-received.loading .dot {\n        display: inline-block;\n        width: 6px;\n        height: 6px;\n        margin: 0 2px;\n        background-color: rgba(0, 0, 0, 0.6); /* Gray dots for typing */\n        border-radius: 50%;\n        animation: dot-bounce 0.6s infinite alternate; /* Typing effect */\n    }\n\n    .msg-received.loading .dot:nth-child(1) {\n        animation-delay: 0s;\n    }\n    .msg-received.loading .dot:nth-child(2) {\n        animation-delay: 0.2s;\n    }\n    .msg-received.loading .dot:nth-child(3) {\n        animation-delay: 0.4s;\n    }\n\n    /* Dot animation */\n    @keyframes dot-bounce {\n        from {\n            transform: translateY(0);\n        }\n        to {\n            transform: translateY(-6px);\n        }\n    }\n\u003C/style>\n   \n\u003Cmain>\n    \u003Cdiv class=\"container\">\n        \u003Cdiv class=\"phone-parent-container\" id=\"phone-container\">\n            \u003Cdiv class=\"phone-child-container\">\n                \u003Cdiv class=\"phone-header\">\n                    \u003Cdiv class=\"phone-header-top\">\n                        \u003C!-- Time on the left -->\n                        \u003Cdiv id=\"phone-header-time\" class=\"phone-header-time\">10:15\u003C/div>\n                        \u003C!-- Camera cutout in the center -->\n                        \u003Cdiv class=\"phone-header-camera\">\u003C/div>\n                    \n                        \u003C!-- Icons on the right -->\n                        \u003Cdiv class=\"phone-header-icons\">\n                            \u003Ci class=\"fa fa-signal\">\u003C/i>\n                            \u003Ci class=\"fa fa-wifi\">\u003C/i>\n                            \u003Ci class=\"fa fa-battery\">\u003C/i>\n                        \u003C/div>\n                    \u003C/div>\n                    \u003C!-- Bottom Layer: Back Button, Name, More Info -->\n                    \u003Cdiv class=\"phone-header-bottom\">\n                        \u003Cdiv class=\"back-button\">\n                            \u003Ci class=\"fa-solid fa-chevron-left\">\u003C/i>\n                        \u003C/div>\n                        \u003Cdiv class=\"phone-header-name\">Bella\u003C/div>\n                        \u003Cdiv class=\"video-button\">\n                            \u003Ci class=\"fa fa-video\">\u003C/i>\n                        \u003C/div>\n                    \u003C/div>\n                \u003C/div>\n                \u003Cdiv class=\"phone-content\">\n                \u003C/div>\n                \u003Cdiv class=\"phone-footer\">\n                    \u003Cdiv class=\"footer-container\">\n                        \u003Cdiv class=\"message-box\">\n                            \u003Ctextarea placeholder=\"Type a message...\" class=\"enter-message\">\u003C/textarea>\n                            \u003Cdiv class=\"send-button\">\n                                Send\n                            \u003C/div>\n                        \u003C/div>\n                    \u003C/div>\n                \u003C/div>\n            \u003C/div>\n        \u003C/div>\n        \u003C!-- Notification text element -->\n        \u003Cdiv class=\"notification-text\" id=\"notification-text\">\n            Notification will appear here\n        \u003C/div>\n    \u003C/div>\n    \u003C!-- Container for confetti -->\n    \u003Cdiv class=\"confetti-container\" id=\"confetti-container\">\u003C/div>\n\u003C/main>\n\u003Cscript>\n    const messages = document.getElementsByClassName(\"msg\");\n    const phoneContent = document.querySelector(\".phone-content\");\n    const messageInput = document.querySelector(\".enter-message\");\n    const sendButton = document.querySelector(\".send-button\");\n    const textArea = document.querySelector('.enter-message');\n    const messageBox = document.querySelector('.message-box');\n    const phoneContainer = document.getElementById('phone-container');\n    const notificationText = document.getElementById('notification-text');\n    const salesRepImage = \"https://i.ibb.co/yFGncKgm/Grace.png\"\n    const customerImageMale = \"https://i.ibb.co/spcmnhrZ/icons8-male-user-96.png\"\n    const customerImageFemale = \"https://i.ibb.co/spcmnhrZ/icons8-male-user-96.png\"\n    const params = new URLSearchParams(window.location.search);\n    const demoId = params.get('id');\n    const requestURL = \"https://automate.axonflash.com/webhook/custom-auto-demo?id=\" + demoId;\n    const time_ms = Date.now();\n    \n    // Wait for response\n    let isWaitingForResponse = true;\n    sendButton.disabled = true;\n    let isFirstUserMessage = true;\n    let customerImage = customerImageMale; // Default image\n\n    let conversationData = {\n        conversationHistory: \"[SYSTEM MESSAGE] Start conversation\",\n        chatGPT: \"\",\n        conversationID: demoId + '_' + time_ms,\n        predictedGender: \"male\" // default\n    }\n\n    let original_height = textArea.scrollHeight;\n\n    // Display preloaded messages with animation\n    for (let i = 0; messages.length > i; i++) {\n        setTimeout(() => {\n            messages[i].classList.add(\"show\");\n            scrollToBottom();\n        }, i * 1000);\n    }\n\n    // Function to add a message to the chat\n    function addMessage(text, isUser = true) {\n        const messageDiv = document.createElement(\"div\");\n        messageDiv.classList.add(\"msg\", isUser ? \"msg-sent\" : \"msg-received\");\n        \n        // Create a container for the message and image\n        const messageContainer = document.createElement(\"div\");\n        messageContainer.style.display = \"flex\";\n\n        if (isUser) {\n            // For sent messages: align to the right\n            messageContainer.style.justifyContent = \"flex-end\";\n            // Image for sent messages\n            const userImage = document.createElement(\"img\");\n            userImage.src = customerImage; \n            userImage.alt = \"User\";\n            userImage.style.borderRadius = \"50%\";\n            userImage.style.width = \"2.5rem\";\n            userImage.style.height = \"2.5rem\";\n            userImage.style.alignSelf = \"flex-end\"; // Ensure image is aligned at the bottom\n            userImage.style.marginRight = \"2px\"\n            messageContainer.appendChild(messageDiv);\n            messageContainer.appendChild(userImage); // Add image to the right\n            \n            // Check if this is the first user message\n            if (isFirstUserMessage) {\n                isFirstUserMessage = false;\n                activateGlowEffect('blue', 'Conversation started: The lead has engaged with ' + 'Bella.');\n            }\n        } else {\n            // For received messages: align to the left\n            messageContainer.style.justifyContent = \"flex-start\";\n            // Image for received messages\n            const receivedImage = document.createElement(\"img\");\n            receivedImage.src = salesRepImage;\n            receivedImage.alt = \"AI\";\n            receivedImage.style.borderRadius = \"50%\";\n            receivedImage.style.width = \"2.5rem\";\n            receivedImage.style.height = \"2.5rem\";\n            receivedImage.style.alignSelf = \"flex-end\"; // Ensure image is aligned at the bottom\n            receivedImage.style.marginLeft = \"2px\"\n            messageContainer.appendChild(receivedImage); // Add image to the left\n            messageContainer.appendChild(messageDiv);\n            \n            // Check for trigger phrases in AI response\n            checkForTriggerPhrases(text);\n            \n        }\n\n        messageDiv.innerHTML = text.replace(/\\n/g, \"\u003Cbr>\");\n\n        // Add animation class after timeout\n        setTimeout(() => {\n            messageDiv.classList.add(\"show\");\n            scrollToBottom(); // Scroll after the message is added\n        }, 10);\n\n        // Append the message to the chat window\n        phoneContent.appendChild(messageContainer);\n    }\n\n    // Function to check for trigger phrases in the AI responses\n    function checkForTriggerPhrases(text) {\n        const lowerText = text.toLowerCase();\n\n        if (lowerText.includes('from our list')) {\n            activateGlowEffect('red', 'Conversation ended: The lead does not want to be contacted any longer');\n            disableMessaging();\n        } \n        else if (lowerText.includes('expect a call')) {\n            activateGlowEffect('green', 'Lead qualified: This lead will be sent instantly to your sales team.');\n            triggerConfetti();\n        }\n        else if (lowerText.includes('have a great day')) {\n            activateGlowEffect('yellow', 'Conversation ended: The lead has completed their interaction with ' + 'Bella.');\n            disableMessaging();\n        }\n        else if (lowerText.includes('notified the team')) {\n            activateGlowEffect('purple', 'Conversation milestone: ' + 'Bella' + ' will notify your team to follow up with the lead for the specific information');\n        } \n    }\n\n    // Function to disable messaging when conversation ends\n    function disableMessaging() {\n        // Disable the text area\n        messageInput.disabled = true;\n        messageInput.style.opacity = \"0.5\";\n        messageInput.placeholder = \"Conversation ended\";\n        \n        // Disable the send button\n        sendButton.disabled = true;\n        sendButton.style.opacity = \"0.5\";\n        sendButton.style.backgroundColor = \"#cccccc\";\n        \n        // Make sure enter key doesn't trigger sending\n        isWaitingForResponse = true;\n    }\n\n    // Function to activate glow effect\n    function activateGlowEffect(color, message) {\n        // Remove any existing glow classes\n        phoneContainer.classList.remove('glow-blue', 'glow-green', 'glow-yellow', 'glow-red', 'glow-purple');\n        \n        // Add the new glow class\n        phoneContainer.classList.add(`glow-${color}`);\n        \n        // Show notification with message\n        notificationText.textContent = message;\n        notificationText.classList.add('notification-visible');\n        \n        // Remove the glow effect after 5 seconds\n        setTimeout(() => {\n            phoneContainer.classList.remove(`glow-${color}`);\n            notificationText.classList.remove('notification-visible');\n        }, 7000);\n    }\n\n    // Function to trigger confetti animation\n    function triggerConfetti() {\n        const duration = 3000;\n        const animationEnd = Date.now() + duration;\n        const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };\n\n        function randomInRange(min, max) {\n            return Math.random() * (max - min) + min;\n        }\n\n        const interval = setInterval(function() {\n            const timeLeft = animationEnd - Date.now();\n\n            if (timeLeft \u003C= 0) {\n                return clearInterval(interval);\n            }\n\n            const particleCount = 50 * (timeLeft / duration);\n            \n            // Confetti burst from center\n            confetti({\n                ...defaults,\n                particleCount,\n                origin: { x: 0.5, y: 0.5 }\n            });\n            \n            // Confetti from sides\n            confetti({\n                ...defaults,\n                particleCount: particleCount * 0.5,\n                origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }\n            });\n            \n            confetti({\n                ...defaults,\n                particleCount: particleCount * 0.5,\n                origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }\n            });\n        }, 250);\n    }\n\n    // Event listener for the Enter key\n    messageInput.addEventListener(\"keydown\", (e) => {\n        if (e.key === \"Enter\" && !isWaitingForResponse) {\n            sendButton.click();\n        }\n    });\n\n    // Function to scroll the chat to the bottom\n    function scrollToBottom() {\n        phoneContent.scrollTo({\n            top: phoneContent.scrollHeight,\n            behavior: \"smooth\",\n        });\n    }\n\n    function resizeMessageContainer(){\n        // textArea.style.height = 'auto';\n        const scrollHeight = textArea.scrollHeight;\n\n        // Update the height of the textarea and message box\n        textArea.style.height = `${scrollHeight}px`;\n        messageBox.style.height = `${scrollHeight + 8}px`; // Adjust for padding\n\n        // Shift the message box upwards\n        const footerContainer = document.querySelector('.footer-container');\n        footerContainer.style.transform = `translateY(-${scrollHeight - 24}px)`;\n    }\n\n    function resetMessageHeight(){\n        // Update the height of the textarea and message box\n        textArea.style.height = `${original_height}px`;\n        messageBox.style.height = `${original_height + 8}px`; // Adjust for padding\n\n        // Shift the message box upwards\n        const footerContainer = document.querySelector('.footer-container');\n        footerContainer.style.transform = `translateY(-${original_height - 24}px)`;\n    }\n\n    textArea.addEventListener('input', () => {\n        resizeMessageContainer()\n    });\n\n    textArea.addEventListener('keydown', (event) => {\n        if (event.key === 'Enter') {\n            event.preventDefault(); // Stop the default behavior (adding a new line)\n        }\n    }); \n\n\n    function updateTime() {\n        const timeElement = document.getElementById(\"phone-header-time\");\n        const now = new Date();\n\n        // Format hours and minutes\n        const hours = now.getHours().toString().padStart(2, \"0\");\n        const minutes = now.getMinutes().toString().padStart(2, \"0\");\n\n        // Update the time in the header\n        timeElement.textContent = `${hours}:${minutes}`;\n    }\n\n    // Call the function initially to set the current time\n    updateTime();\n\n    // Update the time every minute\n    setInterval(updateTime, 60000); // Updates every 60 seconds\n\n    // Event listener for the send button\n    sendButton.addEventListener(\"click\", () => {\n        const userMessage = messageInput.value.trim();\n        if (userMessage && !isWaitingForResponse) {\n            // Set waiting state\n            isWaitingForResponse = true;\n            sendButton.disabled = true;\n\n            addMessage(userMessage); // Add the user's message\n            // Append user's message to the conversation history\n            conversationData[\"conversationHistory\"] += ` | User: ${userMessage}`; \n            sendMessageToWebhook(conversationData);\n            messageInput.value = \"\"; // Clear the input field\n            resetMessageHeight();\n        }\n    });\n\n    function addLoadingMessage() {\n        const loadingDiv = document.createElement(\"div\");\n        loadingDiv.classList.add(\"msg\", \"msg-received\", \"loading\", \"show\");\n\n        // Add the dots for the typing animation\n        loadingDiv.innerHTML = `\n            \u003Cdiv class=\"dot\">\u003C/div>\n            \u003Cdiv class=\"dot\">\u003C/div>\n            \u003Cdiv class=\"dot\">\u003C/div>\n        `;\n\n        // Create a container for the loading message and image\n        const loadingContainer = document.createElement(\"div\");\n        loadingContainer.style.display = \"flex\";\n        loadingContainer.style.justifyContent = \"flex-start\";\n        \n        // Image for received messages\n        const receivedImage = document.createElement(\"img\");\n        receivedImage.src = salesRepImage;\n        receivedImage.alt = \"AI\";\n        receivedImage.style.borderRadius = \"50%\";\n        receivedImage.style.width = \"2.5rem\";\n        receivedImage.style.height = \"2.5rem\";\n        receivedImage.style.alignSelf = \"flex-end\"; // Ensure image is aligned at the bottom\n        receivedImage.style.marginLeft = \"2px\";\n        \n        loadingContainer.appendChild(receivedImage);\n        loadingContainer.appendChild(loadingDiv);\n\n        // Append the loading message to the chat\n        phoneContent.appendChild(loadingContainer);\n\n        // Scroll to the bottom\n        scrollToBottom();\n    }\n\n    function removeLoadingMessage() {\n        const loadingContainer = phoneContent.querySelector(\"div:has(.msg-received.loading)\");\n        if (loadingContainer) {\n            loadingContainer.remove();\n        }\n    }\n\n    function sendMessageToWebhook(message) {\n        let messageDelay = 1000;\n        \n        setTimeout(function() {\n            // Add the loading message\n            addLoadingMessage();\n        }, messageDelay);\n\n        // Send the message to your webhook using fetch\n        fetch(requestURL, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(message)\n            })\n            .then(response => response.json())\n            .then(data => {\n                // Check if the webhook returned a response message\n                if (data) {\n                    conversationData = data; \n                    const wordCount = conversationData.chatGPT.split(/\\s+/).length;\n                    const delay = (wordCount * (60 / 1500) * 1000) + messageDelay; // Delay in milliseconds based on 140 words per minute\n                    if (conversationData.predictedGender == \"female\") {\n                        customerImage = customerImageFemale;\n                    } else {\n                        customerImage = customerImageMale;\n                    }\n                    setTimeout(function() {\n                        // Remove the loading message\n                        removeLoadingMessage();\n                        addMessage(conversationData.chatGPT, false);\n                        // Re-enable send after receiving the AI message\n                        isWaitingForResponse = false;\n                        sendButton.disabled = false;\n                    }, delay);\n                } else {\n                    removeLoadingMessage();\n                    addMessage(\"Sorry, something went wrong.\", false);\n                    isWaitingForResponse = false;\n                    sendButton.disabled = false;\n                }\n            })\n            .catch(error => {\n                removeLoadingMessage();\n                addMessage(\"Sorry, something went wrong.\", false);\n                console.error('Error:', error);\n                // Re-enable send after receiving the AI message\n                isWaitingForResponse = false;\n                sendButton.disabled = false;\n            });\n    }\n\n    \n    sendMessageToWebhook(conversationData);\n\u003C/script>",{"value":421},[],"ccustom-code-F-9ZM6pPpK","custom-code","c-custom-code",{},{"id":349,"type":105,"child":427,"class":429,"styles":434,"extra":444,"wrapper":455,"tagName":142,"meta":105,"title":271},[428],"custom-code--_R3OxCxtx",{"boxShadow":430,"borders":431,"borderRadius":432,"radiusEdge":433},{"value":27},{"value":306},{"value":385},{"value":27},{"paddingLeft":435,"paddingRight":436,"paddingTop":437,"paddingBottom":438,"backgroundColor":439,"width":440,"borderColor":441,"borderWidth":442,"borderStyle":443},{"unit":30,"value":33},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":168},{"value":40},{"value":95,"unit":96},{"value":40},{"value":59,"unit":30},{"value":46},{"visibility":445,"bgImage":447,"columnLayout":449,"justifyContentColumnLayout":450,"alignContentColumnLayout":451,"forceColumnLayoutForMobile":452,"customClass":453},{"value":446},{"hideDesktop":55,"hideMobile":55},{"value":448},{"url":49,"opacity":59,"options":402,"svgCode":49,"servingUrl":49,"placeholderBase64":49,"imageMeta":49},{"value":130},{"value":132},{"value":134},{"value":136},{"value":454},[],{"marginTop":456,"marginBottom":457,"marginRight":458},{"unit":30,"value":33},{"unit":30,"value":33},{"unit":30,"value":31},{"extra":460,"id":428,"meta":423,"tagName":424,"class":469},{"visibility":461,"customCode":463,"customClass":466,"nodeId":468},{"value":462},{"hideMobile":49,"hideDesktop":49},{"value":464},{"rawCustomCode":465},"\u003Cstyle>\n  /* Import Google Fonts for typography */\n  @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');\n\n  .ai-dashboard-info-container {\n    max-width: 650px;\n    margin: 0 auto;\n    padding: 2rem 1.2rem;\n    background-color: #ffffff;\n    border-radius: 12px;\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\n    font-family: 'Poppins', sans-serif;\n  }\n\n  .ai-demo-info-section {\n    text-align: center;\n  }\n\n  .ai-info-heading {\n    font-size: 2rem;\n    font-weight: 700;\n    color: #222;\n    margin-bottom: 1.5rem;\n    line-height: 1.2;\n    letter-spacing: -0.03em;\n    position: relative;\n    display: inline-block;\n  }\n\n  .ai-info-heading::after {\n    content: '';\n    position: absolute;\n    bottom: -8px;\n    left: 50%;\n    transform: translateX(-50%);\n    width: 70px;\n    height: 3px;\n    background: linear-gradient(90deg, #f97150, #f08a28);\n    border-radius: 2px;\n  }\n\n  .ai-info-text {\n    font-family: 'Montserrat', sans-serif;\n    font-size: 1rem;\n    line-height: 1.6;\n    color: #444;\n    margin-bottom: 2rem;\n    max-width: 600px;\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  .ai-info-points {\n    display: flex;\n    flex-direction: column;\n    gap: 1rem;\n    margin-bottom: 2rem;\n  }\n\n  .ai-info-point {\n    display: flex;\n    text-align: left;\n    gap: 1rem;\n    padding: 1.2rem;\n    border-radius: 10px;\n    background-color: #f8f9fa;\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\n    border-left: 4px solid #f97150;\n  }\n\n  .ai-info-point:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.08);\n  }\n\n  .ai-info-icon {\n    flex-shrink: 0;\n    width: 45px;\n    height: 45px;\n    border-radius: 10px;\n    background: linear-gradient(135deg, #f97150, #f08a28);\n    color: white;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 1.4rem;\n    box-shadow: 0 4px 8px rgba(249, 113, 80, 0.3);\n  }\n\n  .ai-info-content {\n    flex: 1;\n  }\n\n  .ai-info-content h3 {\n    color: #222;\n    font-size: 1.1rem;\n    margin-bottom: 0.3rem;\n    font-weight: 600;\n    letter-spacing: -0.01em;\n  }\n\n  .ai-info-content p {\n    color: #555;\n    font-family: 'Montserrat', sans-serif;\n    font-size: 0.9rem;\n    line-height: 1.5;\n  }\n\n  /* Add emphasis to key words */\n  .ai-highlight {\n    color: #f97150;\n    font-weight: 600;\n  }\n\n  .ai-cta-preview {\n    background-color: #f5f8fd;\n    padding: 1.5rem;\n    border-radius: 12px;\n    border-left: 5px solid #f97150;\n    margin-top: 0.5rem;\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);\n  }\n\n  .ai-cta-text {\n    font-size: 1.1rem;\n    font-weight: 600;\n    color: #333;\n    margin-bottom: 1rem;\n    line-height: 1.3;\n  }\n\n  .ai-cta-button-secondary {\n    display: inline-block;\n    padding: 0.75rem 1.5rem;\n    background: linear-gradient(135deg, #f97150, #f08a28);\n    color: white;\n    font-weight: 600;\n    font-size: 0.95rem;\n    text-decoration: none;\n    border-radius: 50px;\n    transition: all 0.3s ease;\n    box-shadow: 0 4px 10px rgba(249, 113, 80, 0.3);\n    letter-spacing: 0.5px;\n    text-transform: uppercase;\n    cursor: pointer;\n    border: none;\n  }\n\n  .ai-cta-button-secondary:hover {\n    transform: translateY(-3px);\n    box-shadow: 0 6px 15px rgba(249, 113, 80, 0.4);\n    background: linear-gradient(135deg, #e85a38, #e07718);\n  }\n\n  /* Modal Styles */\n  .delegate-modal {\n    display: none;\n    position: fixed;\n    z-index: 1000;\n    left: 0;\n    top: 0;\n    width: 100%;\n    height: 100%;\n    background-color: rgba(0, 0, 0, 0.5);\n    opacity: 0;\n    transition: opacity 0.3s ease;\n  }\n\n  .delegate-modal.active {\n    display: flex;\n    opacity: 1;\n    align-items: center;\n    justify-content: center;\n  }\n\n  .delegate-modal-content {\n    background-color: #fff;\n    padding: 2rem;\n    border-radius: 12px;\n    width: 90%;\n    max-width: 500px;\n    position: relative;\n    transform: translateY(50px);\n    transition: transform 0.3s ease;\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  }\n\n  .delegate-modal.active .delegate-modal-content {\n    transform: translateY(0);\n  }\n\n  .close-button {\n    position: absolute;\n    right: 1rem;\n    top: 1rem;\n    font-size: 1.5rem;\n    color: #666;\n    cursor: pointer;\n    background: none;\n    border: none;\n    padding: 0.5rem;\n    line-height: 1;\n  }\n\n  .close-button:hover {\n    color: #333;\n  }\n\n  .modal-header {\n    text-align: center;\n    margin-bottom: 1.5rem;\n  }\n\n  .modal-title {\n    font-size: 1.8rem;\n    font-weight: 700;\n    color: #222;\n    margin-bottom: 0.5rem;\n    font-family: 'Poppins', sans-serif;\n  }\n\n  .modal-subtitle {\n    font-size: 1.2rem;\n    color: #555;\n    margin-bottom: 1rem;\n    font-family: 'Montserrat', sans-serif;\n  }\n\n  .modal-description {\n    font-size: 1rem;\n    color: #666;\n    margin-bottom: 2rem;\n    line-height: 1.6;\n    font-family: 'Montserrat', sans-serif;\n  }\n\n  .form-group {\n    margin-bottom: 1.5rem;\n  }\n\n  .form-group label {\n    display: block;\n    margin-bottom: 0.5rem;\n    color: #333;\n    font-weight: 600;\n    font-family: 'Poppins', sans-serif;\n  }\n\n  .form-group input,\n  .form-group textarea,\n  .form-group select {\n    width: 100%;\n    padding: 0.75rem;\n    border: 1px solid #ddd;\n    border-radius: 8px;\n    font-size: 1rem;\n    font-family: 'Montserrat', sans-serif;\n    transition: border-color 0.3s ease;\n  }\n\n  .form-group input:focus,\n  .form-group textarea:focus,\n  .form-group select:focus {\n    border-color: #f97150;\n    outline: none;\n    box-shadow: 0 0 0 3px rgba(249, 113, 80, 0.1);\n  }\n\n  .form-group textarea {\n    min-height: 100px;\n    resize: vertical;\n  }\n\n  /* Phone input group styling */\n  .phone-input-group {\n    display: flex;\n    gap: 0.5rem;\n  }\n\n  .country-code-select {\n    flex: 0 0 140px;\n  }\n\n  .phone-number-input {\n    flex: 1;\n  }\n\n  .submit-button {\n    width: 100%;\n    padding: 1rem;\n    background: linear-gradient(135deg, #f97150, #f08a28);\n    color: white;\n    font-weight: 600;\n    font-size: 1rem;\n    border: none;\n    border-radius: 50px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-family: 'Poppins', sans-serif;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 0.5rem;\n  }\n\n  .submit-button:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 15px rgba(249, 113, 80, 0.4);\n    background: linear-gradient(135deg, #e85a38, #e07718);\n  }\n\n  .submit-button.loading {\n    opacity: 0.8;\n    cursor: not-allowed;\n  }\n\n  .success-message, .error-message {\n    text-align: center;\n    padding: 1rem;\n    border-radius: 8px;\n    margin-bottom: 1rem;\n    font-family: 'Montserrat', sans-serif;\n  }\n\n  .success-message {\n    background-color: #dff0d8;\n    color: #3c763d;\n    border: 1px solid #d6e9c6;\n  }\n\n  .error-message {\n    background-color: #f2dede;\n    color: #a94442;\n    border: 1px solid #ebccd1;\n  }\n\n  @media (max-width: 768px) {\n    .ai-info-point {\n      flex-direction: column;\n      align-items: center;\n      text-align: center;\n    }\n    \n    .ai-info-icon {\n      margin-bottom: 0.75rem;\n    }\n    \n    .ai-info-heading {\n      font-size: 1.8rem;\n    }\n    \n    .ai-cta-button-secondary {\n      padding: 0.7rem 1.4rem;\n      font-size: 0.9rem;\n    }\n\n    .delegate-modal-content {\n      padding: 1.5rem;\n    }\n\n    .modal-title {\n      font-size: 1.5rem;\n    }\n\n    .modal-subtitle {\n      font-size: 1.1rem;\n    }\n\n    .phone-input-group {\n      flex-direction: column;\n    }\n\n    .country-code-select {\n      flex: 1;\n    }\n  }\n\u003C/style>\n\n\u003Cdiv class=\"ai-dashboard-info-container\">\n  \u003Cdiv class=\"ai-demo-info-section\">\n    \u003Ch2 class=\"ai-info-heading\">This Is Just The Beginning\u003C/h2>\n    \n    \u003Cp class=\"ai-info-text\">\n      What you're seeing now is just \u003Cspan class=\"ai-highlight\">scratching the surface\u003C/span> of what Bella can do. This demo showcases AI-powered conversations that can transform your lead engagement.\n    \u003C/p>\n    \n    \u003Cdiv class=\"ai-info-points\">\n      \u003Cdiv class=\"ai-info-point\">\n        \u003Cdiv class=\"ai-info-icon\">\n          \u003Ci class=\"fas fa-puzzle-piece\">\u003C/i>\n        \u003C/div>\n        \u003Cdiv class=\"ai-info-content\">\n          \u003Ch3>Custom-Built For Your Business\u003C/h3>\n          \u003Cp>We tailor Bella to your industry, products, and sales process to maximize your unique value proposition.\u003C/p>\n        \u003C/div>\n      \u003C/div>\n      \n      \u003Cdiv class=\"ai-info-point\">\n        \u003Cdiv class=\"ai-info-icon\">\n          \u003Ci class=\"fas fa-chart-line\">\u003C/i>\n        \u003C/div>\n        \u003Cdiv class=\"ai-info-content\">\n          \u003Ch3>Focused On Your ROI\u003C/h3>\n          \u003Cp>Our success is measured by your \u003Cspan class=\"ai-highlight\">bottom line\u003C/span>. We optimise for higher conversion rates and qualified leads.\u003C/p>\n        \u003C/div>\n      \u003C/div>\n    \n      \u003Cdiv class=\"ai-cta-preview\">\n        \u003Cp class=\"ai-cta-text\">Ready to see how Bella can be built for your business?\u003C/p>\n        \u003Cbutton onclick=\"openDelegateModal()\" class=\"ai-cta-button-secondary\">DELEGATE IT IN ONE CLICK!\u003C/button>\n      \u003C/div>\n    \u003C/div>\n  \u003C/div>\n\u003C/div>\n\n\u003C!-- Modal for delegating to colleague -->\n\u003Cdiv id=\"delegateModal\" class=\"delegate-modal\">\n  \u003Cdiv class=\"delegate-modal-content\">\n    \u003Cbutton class=\"close-button\" onclick=\"closeDelegateModal()\">&times;\u003C/button>\n    \n    \u003Cdiv class=\"modal-header\">\n      \u003Ch2 class=\"modal-title\">Delegate It in One Click!\u003C/h2>\n      \u003Ch3 class=\"modal-subtitle\">Let the Right Team Member Take Over\u003C/h3>\n      \u003Cp class=\"modal-description\">\n        See the potential, but you're not the one who'll set it up?\u003Cbr>\n        Send this to the right person on your team — we'll take it from there.\n      \u003C/p>\n    \u003C/div>\n    \n    \u003Cdiv id=\"messageContainer\">\u003C/div>\n    \n    \u003Cform id=\"delegateForm\" onsubmit=\"submitDelegateForm(event)\">\n      \u003Cdiv class=\"form-group\">\n        \u003Clabel for=\"colleague-name\">Colleague's Name *\u003C/label>\n        \u003Cinput type=\"text\" id=\"colleague-name\" required>\n      \u003C/div>\n      \n      \u003Cdiv class=\"form-group\">\n        \u003Clabel for=\"colleague-email\">Email Address *\u003C/label>\n        \u003Cinput type=\"email\" id=\"colleague-email\" required>\n      \u003C/div>\n      \n      \u003Cdiv class=\"form-group\">\n        \u003Clabel for=\"colleague-phone\">Phone Number *\u003C/label>\n        \u003Cdiv class=\"phone-input-group\">\n          \u003Cselect id=\"country-code\" class=\"country-code-select\" required>\n            \u003Coption value=\"+1\">USA (+1)\u003C/option>\n            \u003Coption value=\"+44\">UK (+44)\u003C/option>\n            \u003Coption value=\"+61\">AUS (+61)\u003C/option>\n            \u003Coption value=\"+1\">CAN (+1)\u003C/option>\n            \u003Coption value=\"+33\">FRA (+33)\u003C/option>\n            \u003Coption value=\"+49\">DEU (+49)\u003C/option>\n            \u003Coption value=\"+39\">ITA (+39)\u003C/option>\n            \u003Coption value=\"+34\">ESP (+34)\u003C/option>\n            \u003Coption value=\"+31\">NLD (+31)\u003C/option>\n            \u003Coption value=\"+46\">SWE (+46)\u003C/option>\n            \u003Coption value=\"+47\">NOR (+47)\u003C/option>\n            \u003Coption value=\"+45\">DNK (+45)\u003C/option>\n            \u003Coption value=\"+358\">FIN (+358)\u003C/option>\n            \u003Coption value=\"+41\">CHE (+41)\u003C/option>\n            \u003Coption value=\"+43\">AUT (+43)\u003C/option>\n            \u003Coption value=\"+32\">BEL (+32)\u003C/option>\n            \u003Coption value=\"+353\">IRL (+353)\u003C/option>\n            \u003Coption value=\"+351\">PRT (+351)\u003C/option>\n            \u003Coption value=\"+48\">POL (+48)\u003C/option>\n            \u003Coption value=\"+420\">CZE (+420)\u003C/option>\n            \u003Coption value=\"+36\">HUN (+36)\u003C/option>\n            \u003Coption value=\"+30\">GRC (+30)\u003C/option>\n            \u003Coption value=\"+7\">RUS (+7)\u003C/option>\n            \u003Coption value=\"+81\">JPN (+81)\u003C/option>\n            \u003Coption value=\"+86\">CHN (+86)\u003C/option>\n            \u003Coption value=\"+82\">KOR (+82)\u003C/option>\n            \u003Coption value=\"+91\">IND (+91)\u003C/option>\n            \u003Coption value=\"+65\">SGP (+65)\u003C/option>\n            \u003Coption value=\"+60\">MYS (+60)\u003C/option>\n            \u003Coption value=\"+62\">IDN (+62)\u003C/option>\n            \u003Coption value=\"+66\">THA (+66)\u003C/option>\n            \u003Coption value=\"+84\">VNM (+84)\u003C/option>\n            \u003Coption value=\"+63\">PHL (+63)\u003C/option>\n            \u003Coption value=\"+64\">NZL (+64)\u003C/option>\n            \u003Coption value=\"+55\">BRA (+55)\u003C/option>\n            \u003Coption value=\"+52\">MEX (+52)\u003C/option>\n            \u003Coption value=\"+54\">ARG (+54)\u003C/option>\n            \u003Coption value=\"+56\">CHL (+56)\u003C/option>\n            \u003Coption value=\"+57\">COL (+57)\u003C/option>\n            \u003Coption value=\"+58\">VEN (+58)\u003C/option>\n            \u003Coption value=\"+51\">PER (+51)\u003C/option>\n            \u003Coption value=\"+971\">ARE (+971)\u003C/option>\n            \u003Coption value=\"+966\">SAU (+966)\u003C/option>\n            \u003Coption value=\"+27\">ZAF (+27)\u003C/option>\n            \u003Coption value=\"+234\">NGA (+234)\u003C/option>\n            \u003Coption value=\"+20\">EGY (+20)\u003C/option>\n          \u003C/select>\n          \u003Cinput type=\"tel\" id=\"colleague-phone\" class=\"phone-number-input\" placeholder=\"Phone number\" required>\n        \u003C/div>\n      \u003C/div>\n      \n      \u003Cdiv class=\"form-group\">\n        \u003Clabel for=\"personal-message\">Personal Message (optional)\u003C/label>\n        \u003Ctextarea id=\"personal-message\" placeholder=\"Add a personal note to your colleague...\">\u003C/textarea>\n      \u003C/div>\n      \n      \u003Cbutton type=\"submit\" class=\"submit-button\" id=\"submitButton\">\n        ➡️ Send to My Colleague\n      \u003C/button>\n    \u003C/form>\n  \u003C/div>\n\u003C/div>\n\n\u003C!-- Add Font Awesome for icons -->\n\u003Clink rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\" />\n\n\u003Cscript>\nfunction openDelegateModal() {\n  const modal = document.getElementById('delegateModal');\n  modal.classList.add('active');\n  document.body.style.overflow = 'hidden'; // Prevent background scrolling\n}\n\nfunction closeDelegateModal() {\n  const modal = document.getElementById('delegateModal');\n  modal.classList.remove('active');\n  document.body.style.overflow = ''; // Restore scrolling\n}\n\nfunction getIdFromUrl() {\n  const urlParams = new URLSearchParams(window.location.search);\n  return urlParams.get('id');\n}\n\nfunction showMessage(message, isError = false) {\n  const messageContainer = document.getElementById('messageContainer');\n  messageContainer.innerHTML = `\n    \u003Cdiv class=\"${isError ? 'error-message' : 'success-message'}\">\n      ${message}\n    \u003C/div>\n  `;\n}\n\nasync function submitDelegateForm(event) {\n  event.preventDefault();\n  \n  const submitButton = document.getElementById('submitButton');\n  const messageContainer = document.getElementById('messageContainer');\n  \n  // Clear any previous messages\n  messageContainer.innerHTML = '';\n  \n  // Disable button and show loading state\n  submitButton.disabled = true;\n  submitButton.classList.add('loading');\n  submitButton.innerHTML = '⏳ Sending...';\n  \n  // Get form values\n  const name = document.getElementById('colleague-name').value;\n  const email = document.getElementById('colleague-email').value;\n  const countryCode = document.getElementById('country-code').value;\n  const phoneNumber = document.getElementById('colleague-phone').value;\n  const message = document.getElementById('personal-message').value;\n  const demoId = getIdFromUrl();\n  \n  // Combine country code and phone number\n  const fullPhoneNumber = `${countryCode}${phoneNumber.replace(/^\\+/, '')}`;\n  \n  // Prepare data for sending\n  const formData = {\n    colleagueName: name,\n    colleagueEmail: email,\n    colleaguePhone: fullPhoneNumber,\n    personalMessage: message,\n    demoId: demoId,\n    // Add current page URL for reference\n    currentUrl: window.location.href\n  };\n  \n  try {\n    const response = await fetch('https://automate.axonflash.com/webhook/share-demo-to-colleague', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(formData)\n    });\n    \n    if (response.ok) {\n      // Success\n      showMessage('Demo successfully sent to your colleague!');\n      \n      // Reset form\n      document.getElementById('delegateForm').reset();\n      \n      // Close modal after delay\n      setTimeout(() => {\n        closeDelegateModal();\n        messageContainer.innerHTML = '';\n      }, 2000);\n    } else {\n      // Error\n      const errorData = await response.json().catch(() => null);\n      const errorMessage = errorData?.message || 'An error occurred while sending the information.';\n      showMessage(errorMessage, true);\n    }\n  } catch (error) {\n    console.error('Error:', error);\n    showMessage('Unable to connect to the server. Please try again later.', true);\n  } finally {\n    // Re-enable button and restore original text\n    submitButton.disabled = false;\n    submitButton.classList.remove('loading');\n    submitButton.innerHTML = '➡️ Send to My Colleague';\n  }\n}\n\n// Close modal when clicking outside\nwindow.onclick = function(event) {\n  const modal = document.getElementById('delegateModal');\n  if (event.target === modal) {\n    closeDelegateModal();\n  }\n}\n\n// Close modal on escape key\ndocument.addEventListener('keydown', function(event) {\n  if (event.key === 'Escape') {\n    closeDelegateModal();\n  }\n});\n\u003C/script>",{"value":467},[],"ccustom-code--_R3OxCxtx",{},{"id":314,"type":69,"child":471,"class":473,"styles":478,"extra":489,"wrapper":497,"tagName":102,"meta":69,"title":503,"mobileStyles":504,"mobileWrapper":505},[472],"col-DkXLioh8YD",{"alignRow":474,"borders":475,"borderRadius":476,"radiusEdge":477},{"value":75},{"value":23},{"value":25},{"value":27},{"boxShadow":479,"paddingLeft":480,"paddingRight":481,"paddingTop":482,"paddingBottom":484,"backgroundColor":485,"borderColor":486,"borderWidth":487,"borderStyle":488},{"value":27},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":483},15,{"unit":30,"value":237},{"value":40},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":490,"bgImage":492,"rowWidth":494,"customClass":495},{"value":491},{"hideDesktop":55,"hideMobile":55},{"value":493},{"mediaType":61,"url":49,"opacity":59,"options":60,"svgCode":49,"videoUrl":49,"videoThumbnail":49,"videoLoop":136},{"value":372,"unit":96},{"value":496},[],{"marginTop":498,"marginBottom":499,"marginLeft":500,"marginRight":502},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":49,"value":501},"auto",{"unit":49,"value":501},"1 Column Row",{},{},{"id":472,"type":105,"child":507,"class":509,"styles":513,"extra":525,"wrapper":538,"tagName":142,"meta":105,"title":143,"mobileStyles":543,"mobileWrapper":544},[508],"custom-code-G7rpcv-W_V",{"borders":510,"borderRadius":511,"radiusEdge":512},{"value":23},{"value":25},{"value":27},{"boxShadow":514,"paddingLeft":515,"paddingRight":517,"paddingTop":518,"paddingBottom":519,"backgroundColor":520,"width":521,"borderColor":522,"borderWidth":523,"borderStyle":524},{"value":27},{"unit":30,"value":516},5,{"value":516,"unit":30},{"unit":30,"value":237},{"unit":30,"value":237},{"value":40},{"value":95,"unit":96},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":526,"bgImage":528,"columnLayout":530,"justifyContentColumnLayout":531,"alignContentColumnLayout":532,"forceColumnLayoutForMobile":533,"customClass":534,"elementVersion":536},{"value":527},{"hideDesktop":55,"hideMobile":55},{"value":529},{"mediaType":61,"url":49,"opacity":59,"options":60,"svgCode":49,"videoUrl":49,"videoThumbnail":49,"videoLoop":136},{"value":130},{"value":132},{"value":134},{"value":136},{"value":535},[],{"value":537},2,{"marginLeft":539,"marginRight":540,"marginTop":541,"marginBottom":542},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{},{},{"extra":546,"id":508,"meta":423,"tagName":424,"class":555},{"visibility":547,"customCode":549,"customClass":552,"nodeId":554},{"value":548},{"hideMobile":49,"hideDesktop":49},{"value":550},{"rawCustomCode":551},"\u003Cstyle>\n    /* Import Google Fonts for more punchy typography */\n    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');\n    \n    .dashboard-header-container {\n      text-align: center;\n      padding: 2.5rem 1rem;\n      margin: 0 auto;\n      max-width: 1200px;\n      font-family: 'Poppins', sans-serif;\n    }\n    \n    .dashboard-title {\n      font-size: 3rem;\n      font-weight: 700;\n      margin-bottom: 1.2rem;\n      color: #222;\n      letter-spacing: -0.03em;\n      line-height: 1.2;\n      text-shadow: 0 1px 2px rgba(0,0,0,0.05);\n    }\n    \n    .company-name {\n      color: #f97150; \n      font-weight: 800;\n      position: relative;\n      display: inline-block;\n    }\n    \n    /* Underline effect for company name */\n    .company-name::after {\n      content: '';\n      position: absolute;\n      left: 0;\n      bottom: -3px;\n      width: 100%;\n      height: 3px;\n      background: linear-gradient(90deg, #f97150, #f08a28);\n      border-radius: 2px;\n    }\n    \n    .loading-indicator {\n      display: inline-block;\n      position: relative;\n      width: 80px;\n      height: 30px;\n    }\n    \n    .loading-indicator div {\n      position: absolute;\n      top: 10px;\n      width: 13px;\n      height: 13px;\n      border-radius: 50%;\n      background: #f97150;\n      animation-timing-function: cubic-bezier(0, 1, 1, 0);\n    }\n    \n    .loading-indicator div:nth-child(1) {\n      left: 8px;\n      animation: loading1 0.6s infinite;\n    }\n    \n    .loading-indicator div:nth-child(2) {\n      left: 8px;\n      animation: loading2 0.6s infinite;\n    }\n    \n    .loading-indicator div:nth-child(3) {\n      left: 32px;\n      animation: loading2 0.6s infinite;\n    }\n    \n    .loading-indicator div:nth-child(4) {\n      left: 56px;\n      animation: loading3 0.6s infinite;\n    }\n    \n    @keyframes loading1 {\n      0% { transform: scale(0); }\n      100% { transform: scale(1); }\n    }\n    \n    @keyframes loading2 {\n      0% { transform: translate(0, 0); }\n      100% { transform: translate(24px, 0); }\n    }\n    \n    @keyframes loading3 {\n      0% { transform: scale(1); }\n      100% { transform: scale(0); }\n    }\n    \n    .error-message {\n      color: #e74c3c;\n      font-weight: 600;\n      font-size: 1.2rem;\n      padding: 0.5rem 1rem;\n      background-color: rgba(231, 76, 60, 0.1);\n      border-radius: 6px;\n      display: inline-block;\n    }\n    \n    /* Subtitle styling */\n    .dashboard-subtitle {\n      font-family: 'Montserrat', sans-serif;\n      font-size: 1.1rem;\n      font-weight: 500;\n      color: #555;\n      margin-top: 0.5rem;\n      max-width: 800px;\n      margin-left: auto;\n      margin-right: auto;\n    }\n    \n    /* Media queries for responsive design */\n    @media (max-width: 768px) {\n      .dashboard-title {\n        font-size: 2.5rem;\n      }\n      \n      .dashboard-subtitle {\n        font-size: 1rem;\n      }\n    }\n    \n    @media (max-width: 480px) {\n      .dashboard-title {\n        font-size: 2rem;\n      }\n    }\n  \u003C/style>\n  \n  \u003Cdiv class=\"dashboard-header-container\">\n    \u003Ch1 id=\"demo-header\" class=\"dashboard-title\">\n      \u003Cdiv class=\"loading-indicator\">\u003Cdiv>\u003C/div>\u003Cdiv>\u003C/div>\u003Cdiv>\u003C/div>\u003Cdiv>\u003C/div>\u003C/div>\n    \u003C/h1>\n    \u003Cp id=\"demo-subtitle\" class=\"dashboard-subtitle\" style=\"display: none;\">\n      Experience the power of AI-driven conversations tailored for your business\n    \u003C/p>\n  \u003C/div>\n  \n  \u003Cscript>\n    async function loadCompanyHeader() {\n      const urlParams = new URLSearchParams(window.location.search);\n      const id = urlParams.get('id');\n      const headerElement = document.getElementById('demo-header');\n      const subtitleElement = document.getElementById('demo-subtitle');\n      \n      if (!id) {\n        headerElement.innerHTML = '\u003Cspan class=\"error-message\">Missing ID in URL\u003C/span>';\n        return;\n      }\n      \n      try {\n        // Replace this with your actual N8N webhook URL\n        const webhookUrl = `https://automate.axonflash.com/webhook/get-demo-info?id=${id}`;\n        \n        const response = await fetch(webhookUrl);\n        if (!response.ok) throw new Error('Network response was not ok');\n        const data = await response.json();\n        const company = data.client_company || 'Unknown Company';\n        \n        // Set the header with the company name in a different color\n        headerElement.innerHTML = `Say Hello to Bella \u003Cbr>Built for \u003Cspan class=\"company-name\">${company}\u003C/span>`;\n        \n        // Show the subtitle\n        subtitleElement.style.display = 'block';\n      } catch (error) {\n        console.error('Failed to load company data:', error);\n        headerElement.innerHTML = '\u003Cspan class=\"error-message\">Error loading company info\u003C/span>';\n      }\n    }\n    \n    window.addEventListener('DOMContentLoaded', loadCompanyHeader);\n  \u003C/script>",{"value":553},[],"ccustom-code-G7rpcv-W_V",{},{"id":11,"type":16,"child":557,"class":559,"styles":565,"extra":578,"wrapper":588,"meta":16,"tagName":66,"title":346,"_id":11},[558],"row-Vw0H17D-8H",{"width":560,"boxShadow":561,"borders":562,"borderRadius":563,"radiusEdge":564},{"value":21},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":566,"paddingRight":567,"paddingBottom":568,"paddingTop":570,"marginTop":571,"marginBottom":572,"backgroundColor":573,"borderColor":575,"borderWidth":576,"borderStyle":577},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":569},20,{"unit":30,"value":569},{"unit":30,"value":31},{"unit":30,"value":31},{"value":574},"var(--color-lkbhfki1)",{"value":42},{"value":44,"unit":30},{"value":46},{"sticky":579,"visibility":580,"bgImage":582,"allowRowMaxWidth":585,"customClass":586},{"value":52},{"value":581},{"hideDesktop":55,"hideMobile":55},{"value":583},{"url":584,"opacity":59,"options":60,"svgCode":49,"showSvgToggle":55,"videoUrl":49},"https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg5.png?alt=media&token=b3a41a19-7a1d-4114-be31-acbdf9d750d3https://firebasestorage.googleapis.com/v0/b/highlevel-staging.appspot.com/o/assets%2Fbackgrounds%2Fgradient%2Fg3.png?alt=media&token=9c1f5561-e962-4487-bbf4-e72a4b52c54b",{"value":55},{"value":587},[344],{},{"id":558,"type":69,"child":590,"class":592,"styles":597,"extra":607,"wrapper":615,"tagName":102,"meta":69,"title":503,"mobileStyles":620,"mobileWrapper":621},[591],"col-eMIP6gTw6q",{"alignRow":593,"borders":594,"borderRadius":595,"radiusEdge":596},{"value":75},{"value":23},{"value":25},{"value":27},{"boxShadow":598,"paddingLeft":599,"paddingRight":600,"paddingTop":601,"paddingBottom":602,"backgroundColor":603,"borderColor":604,"borderWidth":605,"borderStyle":606},{"value":27},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":31},{"unit":30,"value":31},{"value":40},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":608,"bgImage":610,"rowWidth":612,"customClass":613},{"value":609},{"hideDesktop":55,"hideMobile":55},{"value":611},{"mediaType":61,"url":49,"opacity":59,"options":60,"svgCode":49,"videoUrl":49,"videoThumbnail":49,"videoLoop":136},{"value":372,"unit":96},{"value":614},[],{"marginTop":616,"marginBottom":617,"marginLeft":618,"marginRight":619},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":49,"value":501},{"unit":49,"value":501},{},{},{"id":591,"type":105,"child":623,"class":626,"styles":630,"extra":641,"wrapper":653,"tagName":142,"meta":105,"title":143,"mobileStyles":658,"mobileWrapper":659},[624,625],"custom-code-D0vHByKM9n","button-XOKFNf57PF",{"borders":627,"borderRadius":628,"radiusEdge":629},{"value":23},{"value":25},{"value":27},{"boxShadow":631,"paddingLeft":632,"paddingRight":633,"paddingTop":634,"paddingBottom":635,"backgroundColor":636,"width":637,"borderColor":638,"borderWidth":639,"borderStyle":640},{"value":27},{"unit":30,"value":516},{"value":516,"unit":30},{"unit":30,"value":237},{"unit":30,"value":237},{"value":40},{"value":95,"unit":96},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":642,"bgImage":644,"columnLayout":646,"justifyContentColumnLayout":647,"alignContentColumnLayout":648,"forceColumnLayoutForMobile":649,"customClass":650,"elementVersion":652},{"value":643},{"hideDesktop":55,"hideMobile":55},{"value":645},{"mediaType":61,"url":49,"opacity":59,"options":60,"svgCode":49,"videoUrl":49,"videoThumbnail":49,"videoLoop":136},{"value":130},{"value":132},{"value":134},{"value":136},{"value":651},[],{"value":537},{"marginLeft":654,"marginRight":655,"marginTop":656,"marginBottom":657},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{},{},{"extra":661,"id":624,"meta":423,"tagName":424,"class":670},{"visibility":662,"customCode":664,"customClass":667,"nodeId":669},{"value":663},{"hideMobile":49,"hideDesktop":49},{"value":665},{"rawCustomCode":666},"\u003Cstyle>\n    /* Import Google Fonts */\n    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');\n    \n    /* Reset and Base Styles - with unique prefix */\n    .ai-features * {\n        margin: 0;\n        padding: 0;\n        box-sizing: border-box;\n        font-family: 'Poppins', sans-serif;\n    }\n    \n    /* Container Styles - with unique prefix */\n    .ai-features-container {\n        max-width: 1200px;\n        margin: 0 auto;\n        padding: 20px;\n    }\n    \n    /* Header Section - with unique prefix */\n    .ai-features-header {\n        background-color: #ffffff;\n        border-radius: 12px;\n        padding: 40px 30px;\n        text-align: center;\n        margin-bottom: 30px;\n        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\n    }\n    \n    .ai-features-header h1 {\n        font-size: 2rem;\n        margin-bottom: 15px;\n        color: #222;\n        font-weight: 700;\n        letter-spacing: -0.03em;\n        line-height: 1.2;\n        position: relative;\n        display: inline-block;\n    }\n    \n    .ai-features-header h1::after {\n        content: '';\n        position: absolute;\n        bottom: -8px;\n        left: 50%;\n        transform: translateX(-50%);\n        width: 70px;\n        height: 3px;\n        background: linear-gradient(90deg, #f97150, #f08a28);\n        border-radius: 2px;\n    }\n    \n    .ai-features-header h1 span {\n        color: #f97150;\n        font-weight: 800;\n    }\n    \n    .ai-features-header p {\n        font-family: 'Montserrat', sans-serif;\n        font-size: 1rem;\n        max-width: 800px;\n        margin: 1.5rem auto 0;\n        color: #444;\n        line-height: 1.6;\n    }\n    \n    /* Features Grid - with unique prefix */\n    .ai-features-grid {\n        display: grid;\n        grid-template-columns: repeat(2, 1fr);\n        gap: 30px;\n    }\n    \n    .ai-feature-card {\n        background-color: #ffffff;\n        border-radius: 12px;\n        padding: 35px 30px;\n        text-align: center;\n        transition: transform 0.3s ease, box-shadow 0.3s ease;\n        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);\n        height: 100%;\n        display: flex;\n        flex-direction: column;\n        border-left: 4px solid #f97150;\n    }\n    \n    .ai-feature-card:hover {\n        transform: translateY(-10px);\n        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);\n    }\n    \n    .ai-icon-container {\n        margin-bottom: 20px;\n        height: 80px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }\n    \n    .ai-feature-icon {\n        width: 45px;\n        height: 45px;\n        border-radius: 10px;\n        background: linear-gradient(135deg, #f97150, #f08a28);\n        color: white;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 1.4rem;\n        box-shadow: 0 4px 8px rgba(249, 113, 80, 0.3);\n        margin: 0 auto;\n    }\n    \n    .ai-feature-card h2 {\n        font-size: 1.1rem;\n        margin-bottom: 15px;\n        color: #222;\n        font-weight: 600;\n        letter-spacing: -0.01em;\n    }\n    \n    .ai-feature-card p {\n        margin-bottom: 20px;\n        color: #555;\n        font-family: 'Montserrat', sans-serif;\n        font-size: 0.9rem;\n        line-height: 1.5;\n        flex-grow: 1;\n    }\n    \n    .ai-highlight {\n        color: #f97150;\n        font-weight: 600;\n    }\n    \n    .ai-feature-card .ai-tagline {\n        font-weight: 600;\n        color: #222;\n        font-size: 0.95rem;\n        margin-top: auto;\n        padding-top: 15px;\n        border-top: 1px solid #eee;\n    }\n    \n    /* Responsive Design - with unique prefix */\n    @media (max-width: 992px) {\n        .ai-features-header h1 {\n            font-size: 1.8rem;\n        }\n        \n        .ai-features-header p {\n            font-size: 0.95rem;\n        }\n    }\n    \n    @media (max-width: 768px) {\n        .ai-features-grid {\n            grid-template-columns: 1fr;\n        }\n        \n        .ai-features-container {\n            padding: 10px;\n        }\n        \n        .ai-features-header {\n            padding: 30px 20px;\n        }\n        \n        .ai-feature-card {\n            padding: 30px 20px;\n        }\n    }\n    \n    @media (max-width: 480px) {\n        .ai-features-header h1 {\n            font-size: 1.6rem;\n        }\n        \n        .ai-features-header p {\n            font-size: 0.9rem;\n        }\n    }\n    \n    /* Button Styles - with unique prefix */\n    .ai-features-cta-button {\n        display: inline-block;\n        padding: 0.75rem 1.5rem;\n        background: linear-gradient(135deg, #f97150, #f08a28);\n        color: white;\n        font-weight: 600;\n        font-size: 0.95rem;\n        text-decoration: none;\n        border-radius: 50px;\n        transition: all 0.3s ease;\n        box-shadow: 0 4px 10px rgba(249, 113, 80, 0.3);\n        letter-spacing: 0.5px;\n        text-transform: uppercase;\n        border: none;\n        cursor: pointer;\n        margin-top: 20px;\n    }\n    \n    .ai-features-cta-button:hover {\n        transform: translateY(-3px);\n        box-shadow: 0 6px 15px rgba(249, 113, 80, 0.4);\n        background: linear-gradient(135deg, #e85a38, #e07718);\n    }\n\u003C/style>\n\u003C!-- Font Awesome for icons -->\n\u003Clink rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n\n\u003Cdiv class=\"ai-features\">\n    \u003Cdiv class=\"ai-features-container\">\n        \u003Cdiv class=\"ai-features-header\">\n            \u003Ch1>What Else Can \u003Cspan>Bella\u003C/span> Do For You?\u003C/h1>\n            \u003Cp>Everything you need to convert more leads—without hiring more staff.\u003C/p>\n        \u003C/div>\n        \n        \u003Cdiv class=\"ai-features-grid\">\n            \u003C!-- Speed to Lead -->\n            \u003Cdiv class=\"ai-feature-card\">\n                \u003Cdiv class=\"ai-icon-container\">\n                    \u003Cdiv class=\"ai-feature-icon\">\n                        \u003Ci class=\"fas fa-rocket\">\u003C/i>\n                    \u003C/div>\n                \u003C/div>\n                \u003Ch2>Speed to Lead That Actually Converts\u003C/h2>\n                \u003Cp>Every second counts. We respond to your leads within moments—while they're still interested, engaged, and ready to act. Our \u003Cspan class=\"ai-highlight\">data shows\u003C/span> businesses using Bella's speed-to-lead feature see conversion rates increase by up to 78%.\u003C/p>\n                \u003Cdiv class=\"ai-tagline\">Fast Follow-Up = More Sales\u003C/div>\n            \u003C/div>\n            \n            \u003C!-- Voice AI -->\n            \u003Cdiv class=\"ai-feature-card\">\n                \u003Cdiv class=\"ai-icon-container\">\n                    \u003Cdiv class=\"ai-feature-icon\">\n                        \u003Ci class=\"fas fa-phone-alt\">\u003C/i>\n                    \u003C/div>\n                \u003C/div>\n                \u003Ch2>Voice Calls That Feel Human\u003C/h2>\n                \u003Cp>Turn leads into conversations with natural-sounding AI voice calls that connect in seconds. No hold music. No awkward bots. Just smooth, \u003Cspan class=\"ai-highlight\">human-like calls\u003C/span> that qualify and convert.\u003C/p>\n                \u003Cdiv class=\"ai-tagline\">Scalable Conversations. Real Results\u003C/div>\n            \u003C/div>\n            \n            \u003C!-- Review -->\n            \u003Cdiv class=\"ai-feature-card\">\n                \u003Cdiv class=\"ai-icon-container\">\n                    \u003Cdiv class=\"ai-feature-icon\">\n                        \u003Ci class=\"fas fa-star\">\u003C/i>\n                    \u003C/div>\n                \u003C/div>\n                \u003Ch2>Automate Your Reviews\u003C/h2>\n                \u003Cp>Bella follows up with happy customers at the perfect moment—so you get more \u003Cspan class=\"ai-highlight\">5-star reviews\u003C/span> without lifting a finger. Our automated review system captures feedback when satisfaction is highest.\u003C/p>\n                \u003Cdiv class=\"ai-tagline\">Build Trust. Boost Rankings.\u003C/div>\n            \u003C/div>\n            \n            \u003C!-- WhatsApp -->\n            \u003Cdiv class=\"ai-feature-card\">\n                \u003Cdiv class=\"ai-icon-container\">\n                    \u003Cdiv class=\"ai-feature-icon\">\n                        \u003Ci class=\"fab fa-whatsapp\">\u003C/i>\n                    \u003C/div>\n                \u003C/div>\n                \u003Ch2>WhatsApp Engagement\u003C/h2>\n                \u003Cp>Bella connects with your leads through their preferred messaging platform. Our WhatsApp integration delivers \u003Cspan class=\"ai-highlight\">instant responses\u003C/span>, personalized follow-ups, and automated nurturing—all with 98% open rates.\u003C/p>\n                \u003Cdiv class=\"ai-tagline\">Global Reach. Instant Connection.\u003C/div>\n            \u003C/div>\n        \u003C/div>\n    \u003C/div>\n\u003C/div>",{"value":668},[],"ccustom-code-D0vHByKM9n",{},{"extra":672,"id":625,"tagName":294,"meta":295,"class":691},{"visibility":673,"text":675,"subText":676,"productId":677,"action":679,"visitWebsite":680,"hideElements":681,"showElements":682,"scrollToElement":683,"stepPath":684,"saleAction":685,"phoneNumber":686,"emailAddress":687,"customClass":688,"nodeId":690},{"value":674},{"hideMobile":49,"hideDesktop":49},{"value":277},{},{"value":678},{},{"value":282},{},{},{},{"value":13},{},{},{},{},{"value":689},[],"cbutton-XOKFNf57PF",{"buttonBoxShadow":692,"buttonBgStyle":693,"buttonVp":694,"buttonHp":695,"borders":696,"borderRadius":697,"radiusEdge":698,"entranceAnimation":699},{"value":298},{"value":300},{"value":302},{"value":304},{"value":306},{"value":308},{"value":27},{"value":311},{"id":12,"type":16,"child":701,"class":703,"styles":709,"extra":720,"wrapper":729,"meta":16,"tagName":66,"title":346,"_id":12},[702],"row-fXS5rW5fqi",{"width":704,"boxShadow":705,"borders":706,"borderRadius":707,"radiusEdge":708},{"value":21},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":710,"paddingRight":711,"paddingBottom":712,"paddingTop":713,"marginTop":714,"marginBottom":715,"backgroundColor":716,"borderColor":717,"borderWidth":718,"borderStyle":719},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":569},{"unit":30,"value":569},{"unit":30,"value":31},{"unit":30,"value":31},{"value":331},{"value":42},{"value":44,"unit":30},{"value":46},{"sticky":721,"visibility":722,"bgImage":724,"allowRowMaxWidth":726,"customClass":727},{"value":52},{"value":723},{"hideDesktop":55,"hideMobile":55},{"value":725},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":55},{"value":728},[344],{},{"id":702,"type":69,"child":731,"class":733,"styles":738,"extra":748,"wrapper":756,"tagName":102,"meta":69,"title":503,"mobileStyles":761,"mobileWrapper":762},[732],"col-4Uk1NVJdvV",{"alignRow":734,"borders":735,"borderRadius":736,"radiusEdge":737},{"value":75},{"value":23},{"value":25},{"value":27},{"boxShadow":739,"paddingLeft":740,"paddingRight":741,"paddingTop":742,"paddingBottom":743,"backgroundColor":744,"borderColor":745,"borderWidth":746,"borderStyle":747},{"value":27},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":31},{"unit":30,"value":31},{"value":40},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":749,"bgImage":751,"rowWidth":753,"customClass":754},{"value":750},{"hideDesktop":55,"hideMobile":55},{"value":752},{"mediaType":61,"url":49,"opacity":59,"options":60,"svgCode":49,"videoUrl":49,"videoThumbnail":49,"videoLoop":136},{"value":372,"unit":96},{"value":755},[],{"marginTop":757,"marginBottom":758,"marginLeft":759,"marginRight":760},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":49,"value":501},{"unit":49,"value":501},{},{},{"id":732,"type":105,"child":764,"class":767,"styles":771,"extra":782,"wrapper":794,"tagName":142,"meta":105,"title":143,"mobileStyles":799,"mobileWrapper":800},[765,766],"custom-code-IrpQK2lftA","button-cX3vuxtbMw",{"borders":768,"borderRadius":769,"radiusEdge":770},{"value":23},{"value":25},{"value":27},{"boxShadow":772,"paddingLeft":773,"paddingRight":774,"paddingTop":775,"paddingBottom":776,"backgroundColor":777,"width":778,"borderColor":779,"borderWidth":780,"borderStyle":781},{"value":27},{"unit":30,"value":516},{"value":516,"unit":30},{"unit":30,"value":237},{"unit":30,"value":237},{"value":40},{"value":95,"unit":96},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":783,"bgImage":785,"columnLayout":787,"justifyContentColumnLayout":788,"alignContentColumnLayout":789,"forceColumnLayoutForMobile":790,"customClass":791,"elementVersion":793},{"value":784},{"hideDesktop":55,"hideMobile":55},{"value":786},{"mediaType":61,"url":49,"opacity":59,"options":60,"svgCode":49,"videoUrl":49,"videoThumbnail":49,"videoLoop":136},{"value":130},{"value":132},{"value":134},{"value":136},{"value":792},[],{"value":537},{"marginLeft":795,"marginRight":796,"marginTop":797,"marginBottom":798},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{},{},{"extra":802,"id":766,"tagName":294,"meta":295,"class":821},{"visibility":803,"text":805,"subText":806,"productId":807,"action":809,"visitWebsite":810,"hideElements":811,"showElements":812,"scrollToElement":813,"stepPath":814,"saleAction":815,"phoneNumber":816,"emailAddress":817,"customClass":818,"nodeId":820},{"value":804},{"hideMobile":49,"hideDesktop":49},{"value":277},{},{"value":808},{},{"value":282},{},{},{},{"value":13},{},{},{},{},{"value":819},[],"cbutton-cX3vuxtbMw",{"buttonBoxShadow":822,"buttonBgStyle":823,"buttonVp":824,"buttonHp":825,"borders":826,"borderRadius":827,"radiusEdge":828,"entranceAnimation":829},{"value":298},{"value":300},{"value":302},{"value":304},{"value":306},{"value":308},{"value":27},{"value":311},{"extra":831,"id":765,"meta":423,"tagName":424,"class":840},{"visibility":832,"customCode":834,"customClass":837,"nodeId":839},{"value":833},{"hideMobile":49,"hideDesktop":49},{"value":835},{"rawCustomCode":836},"\u003Cscript src=\"https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js\">\u003C/script>\n\u003Cstyle>\n    /* Import Google Fonts */\n    @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');\n    \n    :root {\n        --db-calc-primary-color: #f97150;\n        --db-calc-secondary-color: #555;\n        --db-calc-accent-color: #f08a28;\n        --db-calc-success-color: #28a745;\n        --db-calc-light-color: #f8f9fa;\n        --db-calc-dark-color: #222;\n    }\n    \n    .db-calc-wrapper * {\n        box-sizing: border-box;\n        margin: 0;\n        padding: 0;\n        font-family: 'Poppins', sans-serif;\n    }\n    \n    .db-calc-body {\n        background-color: #fff;\n        color: var(--db-calc-dark-color);\n        line-height: 1.6;\n    }\n    \n    .db-calc-container {\n        max-width: 1200px;\n        margin: 0 auto;\n        padding: 20px;\n    }\n    \n    .db-calc-header {\n        text-align: center;\n        margin-bottom: 40px;\n    }\n    \n    .db-calc-header h1 {\n        color: var(--db-calc-dark-color);\n        margin-bottom: 15px;\n        font-size: 3rem;\n        font-weight: 700;\n        letter-spacing: -0.03em;\n        line-height: 1.2;\n        text-shadow: 0 1px 2px rgba(0,0,0,0.05);\n    }\n    \n    .db-calc-header p {\n        color: var(--db-calc-secondary-color);\n        font-size: 1.1rem;\n        font-family: 'Montserrat', sans-serif;\n        font-weight: 500;\n        max-width: 800px;\n        margin-left: auto;\n        margin-right: auto;\n    }\n    \n    .db-calc-calculator-section {\n        background-color: white;\n        padding: 30px;\n        border-radius: 10px;\n        box-shadow: 0 10px 30px rgba(249, 113, 80, 0.1);\n        animation: db-calc-fadeIn 0.3s;\n        border-top: 4px solid var(--db-calc-primary-color);\n    }\n    \n    @keyframes db-calc-fadeIn {\n        from { opacity: 0; }\n        to { opacity: 1; }\n    }\n    \n    .db-calc-form-group {\n        margin-bottom: 20px;\n    }\n    \n    .db-calc-form-group label {\n        display: block;\n        font-weight: 600;\n        margin-bottom: 8px;\n        color: var(--db-calc-dark-color);\n    }\n    \n    .db-calc-form-control {\n        width: 100%;\n        padding: 12px 15px;\n        border: 1px solid #ced4da;\n        border-radius: 4px;\n        font-size: 16px;\n        transition: border-color 0.15s ease-in-out;\n    }\n    \n    .db-calc-form-control:focus {\n        border-color: var(--db-calc-primary-color);\n        outline: none;\n    }\n    \n    .db-calc-btn {\n        padding: 12px 25px;\n        background: linear-gradient(90deg, var(--db-calc-primary-color), var(--db-calc-accent-color));\n        color: white;\n        border: none;\n        border-radius: 6px;\n        font-size: 16px;\n        font-weight: 600;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    }\n    \n    .db-calc-btn:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);\n    }\n    \n    .db-calc-result-section {\n        margin-top: 30px;\n        background-color: #f8f9ff;\n        padding: 20px;\n        border-radius: 6px;\n        display: none;\n    }\n    \n    .db-calc-result-section h3 {\n        color: var(--db-calc-dark-color);\n        margin-bottom: 15px;\n    }\n    \n    .db-calc-result-item {\n        display: flex;\n        justify-content: space-between;\n        margin-bottom: 10px;\n        padding: 10px;\n        background-color: white;\n        border-radius: 4px;\n        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\n    }\n    \n    .db-calc-result-item .db-calc-label {\n        font-weight: 600;\n    }\n    \n    .db-calc-result-item .db-calc-value {\n        color: var(--db-calc-primary-color);\n        font-weight: 700;\n        position: relative;\n        display: inline-block;\n    }\n    \n    .db-calc-result-item .db-calc-value::after {\n        content: '';\n        position: absolute;\n        left: 0;\n        bottom: -3px;\n        width: 100%;\n        height: 2px;\n        background: linear-gradient(90deg, var(--db-calc-primary-color), var(--db-calc-accent-color));\n        border-radius: 2px;\n        opacity: 0.6;\n    }\n    \n    .db-calc-chart-container {\n        margin-top: 30px;\n        height: 350px;\n    }\n    \n    .db-calc-calculation-notes {\n        margin-top: 30px;\n        background-color: #f0f2ff;\n        padding: 15px;\n        border-radius: 6px;\n        font-size: 14px;\n        color: var(--db-calc-secondary-color);\n    }\n    \n    .db-calc-calculation-notes h4 {\n        margin-bottom: 10px;\n        color: var(--db-calc-dark-color);\n    }\n    \n    .db-calc-currency-row {\n        display: flex;\n        gap: 15px;\n    }\n    \n    .db-calc-currency-row .db-calc-form-group {\n        flex: 1;\n    }\n    \n    @media (max-width: 768px) {\n        .db-calc-calculator-section {\n            padding: 20px;\n        }\n        \n        .db-calc-header h1 {\n            font-size: 2.5rem;\n        }\n        \n        .db-calc-header p {\n            font-size: 1rem;\n        }\n        \n        .db-calc-currency-row {\n            flex-direction: column;\n            gap: 0;\n        }\n    }\n    \n    @media (max-width: 480px) {\n        .db-calc-header h1 {\n            font-size: 2rem;\n        }\n    }\n\u003C/style>\n\n\u003Cdiv class=\"db-calc-wrapper\">\n    \u003Cdiv class=\"db-calc-container\">\n        \u003Cdiv class=\"db-calc-header\">\n            \u003Ch1>Are You Currently Sitting On A\u003Cbr>\u003Cspan style=\"color: var(--db-calc-primary-color); font-weight: 800; position: relative; display: inline-block;\">Gold Mine?\u003Cspan style=\"position: absolute; left: 0; bottom: -3px; width: 100%; height: 3px; background: linear-gradient(90deg, #f97150, #f08a28); border-radius: 2px;\">\u003C/span>\u003C/span>\u003C/h1>\n            \u003Cp>Estimate potential revenue from reviving unconverted leads in your database\u003C/p>\n        \u003C/div>\n        \n        \u003Cdiv class=\"db-calc-calculator-section\">\n            \u003Ch2 style=\"font-family: 'Poppins', sans-serif; font-weight: 700; color: var(--db-calc-dark-color); margin-bottom: 15px;\">Calculate Potential Revenue\u003C/h2>\n            \u003Cp style=\"font-family: 'Montserrat', sans-serif; color: var(--db-calc-secondary-color); margin-bottom: 25px;\">See how much revenue you could generate by reviving old leads in your database\u003C/p>\n            \n            \u003Cdiv class=\"db-calc-form-group\">\n                \u003Clabel for=\"db-calc-old-leads\">Number of Unconverted Leads in Database\u003C/label>\n                \u003Cinput type=\"number\" id=\"db-calc-old-leads\" class=\"db-calc-form-control\" placeholder=\"e.g. 5000\">\n            \u003C/div>\n            \n            \u003Cdiv class=\"db-calc-currency-row\">\n                \u003Cdiv class=\"db-calc-form-group\">\n                    \u003Clabel for=\"db-calc-avg-sale-value\">Average Sale Value\u003C/label>\n                    \u003Cinput type=\"number\" id=\"db-calc-avg-sale-value\" class=\"db-calc-form-control\" placeholder=\"e.g. 1000\">\n                \u003C/div>\n                \n                \u003Cdiv class=\"db-calc-form-group\">\n                    \u003Clabel for=\"db-calc-currency\">Currency\u003C/label>\n                    \u003Cselect id=\"db-calc-currency\" class=\"db-calc-form-control\">\n                        \u003Coption value=\"USD\">USD ($)\u003C/option>\n                        \u003Coption value=\"GBP\">GBP (£)\u003C/option>\n                        \u003Coption value=\"EUR\">EUR (€)\u003C/option>\n                    \u003C/select>\n                \u003C/div>\n            \u003C/div>\n            \n            \u003Cbutton id=\"db-calc-calculate-reactivation\" class=\"db-calc-btn\">Calculate Potential Revenue\u003C/button>\n            \n            \u003Cdiv id=\"db-calc-reactivation-results\" class=\"db-calc-result-section\">\n                \u003Ch3>Potential Revenue from Database Revival\u003C/h3>\n                \n                \u003Cdiv class=\"db-calc-result-item\">\n                    \u003Cdiv class=\"db-calc-label\">Total Unconverted Leads:\u003C/div>\n                    \u003Cdiv class=\"db-calc-value\" id=\"db-calc-total-leads\">0\u003C/div>\n                \u003C/div>\n                \n                \u003Cdiv class=\"db-calc-result-item\">\n                    \u003Cdiv class=\"db-calc-label\">Average Sale Value:\u003C/div>\n                    \u003Cdiv class=\"db-calc-value\" id=\"db-calc-avg-sale\">$0\u003C/div>\n                \u003C/div>\n                \n                \u003Cdiv class=\"db-calc-chart-container\">\n                    \u003Ccanvas id=\"db-calc-reactivation-chart\">\u003C/canvas>\n                \u003C/div>\n                \n                \u003Cdiv class=\"db-calc-calculation-notes\">\n                    \u003Ch4>How this is calculated:\u003C/h4>\n                    \u003Cp>This calculator shows the potential revenue you could generate by converting different percentages of your unconverted leads. The industry average for successful database revival campaigns ranges from 1-8%, depending on the age of the leads, industry, and approach.\u003C/p>\n                    \u003Cp>Each bar represents the potential revenue if you were able to convert that percentage of your previously unconverted leads in your database.\u003C/p>\n                    \u003Cp>For example, if you have 10,000 unconverted leads and an average sale value of 1,000:\u003C/p>\n                    \u003Cul id=\"db-calc-example-list\">\n                        \u003Cli>At 1% conversion: 100 leads × $1,000 = $100,000 revenue\u003C/li>\n                        \u003Cli>At 5% conversion: 500 leads × $1,000 = $500,000 revenue\u003C/li>\n                        \u003Cli>At 8% conversion: 800 leads × $1,000 = $800,000 revenue\u003C/li>\n                    \u003C/ul>\n                    \u003Cp>Most companies can expect conversion rates in the 1-3% range for a revival campaign, with top-performing campaigns reaching 5-8% for warm leads.\u003C/p>\n                \u003C/div>\n            \u003C/div>\n        \u003C/div>\n    \u003C/div>\n\u003C/div>\n\n\u003Cscript>\n    // Database Reactivation Calculator\n    const dbCalcBtn = document.getElementById('db-calc-calculate-reactivation');\n    \n    // Currency symbols mapping\n    const currencySymbols = {\n        'USD': '$',\n        'GBP': '£',\n        'EUR': '€'\n    };\n    \n    dbCalcBtn.addEventListener('click', () => {\n        const oldLeads = parseInt(document.getElementById('db-calc-old-leads').value) || 0;\n        const avgSaleValue = parseFloat(document.getElementById('db-calc-avg-sale-value').value) || 0;\n        const currencySelect = document.getElementById('db-calc-currency');\n        const currency = currencySelect.value;\n        const currencySymbol = currencySymbols[currency];\n        \n        if (oldLeads === 0 || avgSaleValue === 0) {\n            alert('Please enter values for both fields');\n            return;\n        }\n        \n        // Display the results section\n        document.getElementById('db-calc-reactivation-results').style.display = 'block';\n        \n        // Update result values\n        document.getElementById('db-calc-total-leads').textContent = oldLeads.toLocaleString();\n        document.getElementById('db-calc-avg-sale').textContent = `${currencySymbol}${avgSaleValue.toLocaleString()}`;\n        \n        // Update example list with correct currency\n        const exampleList = document.getElementById('db-calc-example-list');\n        exampleList.innerHTML = `\n            \u003Cli>At 1% conversion: 100 leads × ${currencySymbol}1,000 = ${currencySymbol}100,000 revenue\u003C/li>\n            \u003Cli>At 5% conversion: 500 leads × ${currencySymbol}1,000 = ${currencySymbol}500,000 revenue\u003C/li>\n            \u003Cli>At 8% conversion: 800 leads × ${currencySymbol}1,000 = ${currencySymbol}800,000 revenue\u003C/li>\n        `;\n        \n        // Generate data for the chart\n        const conversionRates = [1, 2, 3, 4, 5, 6, 7, 8]; // 1% to 8%\n        const data = conversionRates.map(rate => {\n            const convertedLeads = Math.round(oldLeads * (rate / 100));\n            const revenue = convertedLeads * avgSaleValue;\n            return {\n                conversionRate: rate,\n                revenue: revenue,\n                convertedLeads: convertedLeads\n            };\n        });\n        \n        // Create the chart\n        const ctx = document.getElementById('db-calc-reactivation-chart').getContext('2d');\n        \n        // Destroy existing chart if it exists\n        if (window.dbCalcReactivationChart) {\n            window.dbCalcReactivationChart.destroy();\n        }\n        \n        window.dbCalcReactivationChart = new Chart(ctx, {\n            type: 'bar',\n            data: {\n                labels: conversionRates.map(rate => `${rate}%`),\n                datasets: [{\n                    label: `Potential Revenue (${currencySymbol})`,\n                    data: data.map(d => d.revenue),\n                    backgroundColor: function(context) {\n                        const gradient = ctx.createLinearGradient(0, 0, 0, 400);\n                        gradient.addColorStop(0, 'rgba(249, 113, 80, 0.8)');\n                        gradient.addColorStop(1, 'rgba(240, 138, 40, 0.7)');\n                        return gradient;\n                    },\n                    borderColor: 'rgba(249, 113, 80, 1)',\n                    borderWidth: 1,\n                    borderRadius: 6\n                }]\n            },\n            options: {\n                responsive: true,\n                maintainAspectRatio: false,\n                plugins: {\n                    tooltip: {\n                        callbacks: {\n                            label: function(context) {\n                                const value = context.raw;\n                                return `Potential Revenue: ${currencySymbol}${value.toLocaleString()}`;\n                            },\n                            afterLabel: function(context) {\n                                const index = context.dataIndex;\n                                return `Converted Leads: ${data[index].convertedLeads}`;\n                            }\n                        }\n                    }\n                },\n                scales: {\n                    y: {\n                        beginAtZero: true,\n                        ticks: {\n                            callback: function(value) {\n                                return currencySymbol + value.toLocaleString();\n                            }\n                        },\n                        title: {\n                            display: true,\n                            text: `Potential Revenue (${currencySymbol})`\n                        }\n                    },\n                    x: {\n                        title: {\n                            display: true,\n                            text: 'Conversion Rate'\n                        }\n                    }\n                }\n            }\n        });\n    });\n\u003C/script>",{"value":838},[],"ccustom-code-IrpQK2lftA",{},{"id":13,"type":16,"child":842,"class":844,"styles":849,"extra":863,"wrapper":874,"meta":16,"tagName":66,"title":875,"mobileStyles":876,"mobileWrapper":877,"_id":13},[843],"row-zSC_XVvBwG",{"width":845,"borders":846,"borderRadius":847,"radiusEdge":848},{"value":21},{"value":23},{"value":25},{"value":27},{"boxShadow":850,"paddingLeft":851,"paddingRight":852,"paddingBottom":853,"paddingTop":854,"marginTop":855,"marginBottom":856,"marginLeft":857,"marginRight":858,"backgroundColor":859,"borderColor":860,"borderWidth":861,"borderStyle":862},{"value":27},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":569},{"unit":30,"value":569},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{"value":40},{"value":42},{"value":44,"unit":30},{"value":46},{"sticky":864,"visibility":865,"bgImage":867,"allowRowMaxWidth":869,"customClass":870,"elementScreenshot":872},{"value":52},{"value":866},{"hideDesktop":55,"hideMobile":55},{"value":868},{"mediaType":61,"url":58,"opacity":59,"options":60,"svgCode":49,"videoUrl":49,"videoThumbnail":49,"videoLoop":136,"showSvgToggle":55},{"value":55},{"value":871},[],{"value":873},[],{},"Discovery Call",{},{},{"id":843,"type":69,"child":879,"class":881,"styles":886,"extra":896,"wrapper":904,"tagName":102,"meta":69,"title":503,"mobileStyles":909,"mobileWrapper":910},[880],"col-3YGUrwgtwd",{"alignRow":882,"borders":883,"borderRadius":884,"radiusEdge":885},{"value":75},{"value":23},{"value":25},{"value":27},{"boxShadow":887,"paddingLeft":888,"paddingRight":889,"paddingTop":890,"paddingBottom":891,"backgroundColor":892,"borderColor":893,"borderWidth":894,"borderStyle":895},{"value":27},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":483},{"unit":30,"value":483},{"value":40},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":897,"bgImage":899,"rowWidth":901,"customClass":902},{"value":898},{"hideDesktop":55,"hideMobile":55},{"value":900},{"mediaType":61,"url":49,"opacity":59,"options":60,"svgCode":49,"videoUrl":49,"videoThumbnail":49,"videoLoop":136},{"value":372,"unit":96},{"value":903},[],{"marginTop":905,"marginBottom":906,"marginLeft":907,"marginRight":908},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":49,"value":501},{"unit":49,"value":501},{},{},{"id":880,"type":105,"child":912,"class":914,"styles":918,"extra":929,"wrapper":941,"tagName":142,"meta":105,"title":143,"mobileStyles":946,"mobileWrapper":947},[913],"custom-code-KFlAqIVW_K",{"borders":915,"borderRadius":916,"radiusEdge":917},{"value":23},{"value":25},{"value":27},{"boxShadow":919,"paddingLeft":920,"paddingRight":921,"paddingTop":922,"paddingBottom":923,"backgroundColor":924,"width":925,"borderColor":926,"borderWidth":927,"borderStyle":928},{"value":27},{"unit":30,"value":516},{"value":516,"unit":30},{"unit":30,"value":237},{"unit":30,"value":237},{"value":40},{"value":95,"unit":96},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":930,"bgImage":932,"columnLayout":934,"justifyContentColumnLayout":935,"alignContentColumnLayout":936,"forceColumnLayoutForMobile":937,"customClass":938,"elementVersion":940},{"value":931},{"hideDesktop":55,"hideMobile":55},{"value":933},{"mediaType":61,"url":49,"opacity":59,"options":60,"svgCode":49,"videoUrl":49,"videoThumbnail":49,"videoLoop":136},{"value":130},{"value":132},{"value":134},{"value":136},{"value":939},[],{"value":537},{"marginLeft":942,"marginRight":943,"marginTop":944,"marginBottom":945},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{"unit":30,"value":31},{},{},{"extra":949,"id":913,"meta":423,"tagName":424,"class":958},{"visibility":950,"customCode":952,"customClass":955,"nodeId":957},{"value":951},{"hideMobile":49,"hideDesktop":49},{"value":953},{"rawCustomCode":954},"\u003Cstyle>\n        /* Import Google Fonts */\n        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap');\n        \n        /* CTA Section with unique prefix */\n        .ai-cta-section {\n            font-family: 'Poppins', sans-serif;\n            max-width: 1200px;\n            margin: 60px auto 40px;\n            padding: 0 20px;\n        }\n        \n        .ai-cta-header {\n            text-align: center;\n            margin-bottom: 40px;\n            background-color: rgba(255, 255, 255, 0.95);\n            padding: 30px;\n            border-radius: 12px;\n            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\n        }\n        \n        .ai-cta-title {\n            font-size: 2.2rem;\n            font-weight: 700;\n            color: #222;\n            margin-bottom: 15px;\n            line-height: 1.2;\n            letter-spacing: -0.03em;\n            position: relative;\n            display: inline-block;\n        }\n        \n        .ai-cta-title::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 100px;\n            height: 3px;\n            background: linear-gradient(90deg, #f97150, #f08a28);\n            border-radius: 2px;\n        }\n        \n        .ai-cta-subtitle {\n            font-family: 'Montserrat', sans-serif;\n            font-size: 1.1rem;\n            max-width: 700px;\n            margin: 1.5rem auto 0;\n            color: #333;\n            line-height: 1.6;\n        }\n        \n        .ai-cta-highlight {\n            color: #f97150;\n            font-weight: 700;\n        }\n        \n        .ai-calendly-container {\n            background-color: #ffffff;\n            border-radius: 12px;\n            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\n            padding: 30px;\n            border: 2px solid #f97150;\n            border-radius: 12px;\n            overflow: hidden;\n        }\n        \n        @media (max-width: 768px) {\n            .ai-cta-title {\n                font-size: 1.8rem;\n            }\n            \n            .ai-cta-subtitle {\n                font-size: 0.95rem;\n            }\n            \n            .ai-calendly-container {\n                padding: 20px;\n            }\n            \n            .ai-cta-header {\n                padding: 20px;\n            }\n        }\n        \n        @media (max-width: 480px) {\n            .ai-cta-title {\n                font-size: 1.6rem;\n            }\n            \n            .ai-cta-subtitle {\n                font-size: 0.9rem;\n            }\n            \n            .ai-cta-section {\n                padding: 0 10px;\n            }\n        }\n\u003C/style>\n\n\u003Cdiv class=\"ai-cta-section\">\n    \u003Cdiv class=\"ai-cta-header\">\n        \u003Ch2 class=\"ai-cta-title\">Schedule Your \u003Cspan class=\"ai-cta-highlight\">Discovery Call\u003C/span>\u003C/h2>\n        \u003Cp class=\"ai-cta-subtitle\">Get your personalized AI lead follow-up demo and see exactly how Bella can boost your conversion rates.\u003C/p>\n    \u003C/div>\n    \n    \u003Cdiv class=\"ai-calendly-container\">\n        \u003C!-- Calendly inline widget begin -->\n        \u003Cdiv class=\"calendly-inline-widget\" data-url=\"https://calendly.com/nextlevelgrowthpartners/30min\" style=\"min-width:320px;height:700px;\">\u003C/div>\n        \u003Cscript type=\"text/javascript\" src=\"https://assets.calendly.com/assets/external/widget.js\" async>\u003C/script>\n        \u003C!-- Calendly inline widget end -->\n    \u003C/div>\n\u003C/div>",{"value":956},[],"ccustom-code-KFlAqIVW_K",{},{"id":14,"type":16,"child":960,"class":962,"styles":968,"extra":981,"wrapper":991,"meta":16,"tagName":66,"title":992,"_id":14,"isGlobal":136},[961],"row-Jpvu0Q0cfHn",{"width":963,"boxShadow":964,"borders":965,"borderRadius":966,"radiusEdge":967},{"value":21},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":969,"paddingRight":970,"paddingBottom":971,"paddingTop":973,"marginTop":975,"marginBottom":976,"backgroundColor":977,"borderColor":978,"borderWidth":979,"borderStyle":980},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":972},52,{"unit":30,"value":974},"51",{"unit":30,"value":31},{"unit":30,"value":31},{"value":574},{"value":42},{"value":44,"unit":30},{"value":46},{"sticky":982,"visibility":983,"bgImage":985,"allowRowMaxWidth":987,"customClass":988},{"value":52},{"value":984},{"hideDesktop":55,"hideMobile":55},{"value":986},{"url":58,"opacity":59,"options":60,"svgCode":49,"showSvgToggle":55,"videoUrl":49},{"value":55},{"value":989},[990],"pb-footer",{},"Footer",{"id":961,"type":69,"child":994,"class":998,"styles":1004,"extra":1013,"wrapper":1021,"tagName":102,"meta":69,"title":1024},[995,996,997],"col-hC6omOqyxcy","col-xfeMEp_LXBJ","col-PiXgaEIcg0E",{"alignRow":999,"boxShadow":1000,"borders":1001,"borderRadius":1002,"radiusEdge":1003},{"value":75},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":1005,"paddingRight":1006,"paddingTop":1007,"paddingBottom":1008,"backgroundColor":1009,"borderColor":1010,"borderWidth":1011,"borderStyle":1012},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":33},{"unit":30,"value":33},{"value":40},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":1014,"bgImage":1016,"rowWidth":1018,"customClass":1019},{"value":1015},{"hideDesktop":55,"hideMobile":55},{"value":1017},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":372,"unit":96},{"value":1020},[],{"marginTop":1022,"marginBottom":1023},{"unit":30,"value":31},{"unit":30,"value":31},"3 Column Row",{"id":997,"type":105,"child":1026,"class":1028,"styles":1033,"extra":1044,"wrapper":1055,"tagName":142,"meta":105,"title":1058},[1027],"button-2MeJJFeA3Dd",{"boxShadow":1029,"borders":1030,"borderRadius":1031,"radiusEdge":1032},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":1034,"paddingRight":1035,"paddingTop":1036,"paddingBottom":1037,"backgroundColor":1038,"width":1039,"borderColor":1041,"borderWidth":1042,"borderStyle":1043},{"unit":30,"value":33},{"value":33,"unit":30},{"unit":30,"value":33},{"unit":30,"value":33},{"value":40},{"value":1040,"unit":96},21.4,{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":1045,"bgImage":1047,"columnLayout":1049,"justifyContentColumnLayout":1050,"alignContentColumnLayout":1051,"forceColumnLayoutForMobile":1052,"customClass":1053},{"value":1046},{"hideDesktop":55,"hideMobile":55},{"value":1048},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":69},{"value":132},{"value":132},{"value":136},{"value":1054},[],{"marginTop":1056,"marginBottom":1057},{"unit":30,"value":31},{"unit":30,"value":31},"3rd Column",{"extra":1060,"id":1027,"tagName":294,"meta":295,"class":1081},{"visibility":1061,"text":1063,"subText":1064,"productId":1065,"action":1067,"visitWebsite":1068,"hideElements":1071,"showElements":1072,"scrollToElement":1073,"stepPath":1074,"saleAction":1075,"phoneNumber":1076,"emailAddress":1077,"customClass":1078,"nodeId":1080},{"value":1062},{"hideMobile":49,"hideDesktop":49},{},{},{"value":1066},{},{"value":154},{"value":1069},{"url":1070,"newTab":136},"https://www.instagram.com/nextlevelgrowthpartners/?hl=en",{},{},{},{},{},{},{},{"value":1079},[],"cbutton-2MeJJFeA3Dd",{"buttonEffects":1082,"buttonBoxShadow":1084,"buttonBgStyle":1086,"buttonVp":1087,"buttonHp":1088,"borders":1089,"borderRadius":1090,"radiusEdge":1092},{"value":1083},"buttonElevate",{"value":1085},"btnshadow",{"value":300},{"value":302},{"value":304},{"value":306},{"value":1091},"radius20",{"value":27},{"id":996,"type":105,"child":1094,"class":1096,"styles":1101,"extra":1112,"wrapper":1124,"tagName":142,"meta":105,"title":271},[1095],"paragraph-7jGhmntDzb",{"boxShadow":1097,"borders":1098,"borderRadius":1099,"radiusEdge":1100},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":1102,"paddingRight":1103,"paddingTop":1104,"paddingBottom":1105,"backgroundColor":1106,"width":1107,"borderColor":1109,"borderWidth":1110,"borderStyle":1111},{"unit":30,"value":33},{"value":33,"unit":30},{"unit":30,"value":33},{"unit":30,"value":33},{"value":40},{"value":1108,"unit":96},40.3,{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":1113,"bgImage":1115,"columnLayout":1117,"justifyContentColumnLayout":1118,"alignContentColumnLayout":1120,"forceColumnLayoutForMobile":1121,"customClass":1122},{"value":1114},{"hideDesktop":55,"hideMobile":55},{"value":1116},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":130},{"value":1119},"flex-start",{"value":134},{"value":136},{"value":1123},[],{"marginTop":1125,"marginBottom":1126},{"unit":30,"value":31},{"unit":30,"value":1127},"10",{"extra":1129,"id":1095,"tagName":1137,"class":1138,"meta":1143},{"visibility":1130,"text":1132,"nodeId":1134,"customClass":1135},{"value":1131},{"hideMobile":49,"hideDesktop":49},{"value":1133},"\u003Cp>Contacts:\u003C/p>\u003Cp>\u003C/p>\u003Cp>************\u003C/p>\u003Cp><EMAIL>\u003C/p>\u003Cp>\u003C/p>\u003Cp>************\u003C/p>\u003Cp><EMAIL>\u003C/p>\u003Cp>\u003C/p>\u003Cp>\u003Ca target=\"_blank\" rel=\"noopener noreferrer nofollow\" href=\"https://nextlevelgrowthpartner.com/privacypolicy\">Terms of Service\u003C/a> | \u003Ca target=\"_blank\" rel=\"noopener noreferrer nofollow\" href=\"https://nextlevelgrowthpartner.com/privacypolicy\">Privacy Policy\u003C/a>\u003C/p>","cparagraph-7jGhmntDzb",{"value":1136},[],"c-paragraph",{"borders":1139,"borderRadius":1140,"radiusEdge":1141,"entranceAnimation":1142},{"value":23},{"value":25},{"value":27},{"value":183},"paragraph",{"id":995,"type":105,"child":1145,"class":1147,"styles":1152,"extra":1163,"wrapper":1174,"tagName":142,"meta":105,"title":143},[1146],"image-sH5K7FFn_vn",{"boxShadow":1148,"borders":1149,"borderRadius":1150,"radiusEdge":1151},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":1153,"paddingRight":1154,"paddingTop":1155,"paddingBottom":1156,"backgroundColor":1157,"width":1158,"borderColor":1160,"borderWidth":1161,"borderStyle":1162},{"unit":30,"value":232},{"value":232,"unit":30},{"unit":30,"value":33},{"unit":30,"value":33},{"value":40},{"value":1159,"unit":96},38.4,{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":1164,"bgImage":1166,"columnLayout":1168,"justifyContentColumnLayout":1169,"alignContentColumnLayout":1170,"forceColumnLayoutForMobile":1171,"customClass":1172},{"value":1165},{"hideDesktop":55,"hideMobile":55},{"value":1167},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":130},{"value":132},{"value":134},{"value":136},{"value":1173},[],{"marginTop":1175,"marginBottom":1176},{"unit":30,"value":31},{"unit":30,"value":31},{"extra":1178,"id":1146,"meta":61,"tagName":1191,"class":1192},{"nodeId":1179,"visibility":1180,"imageActions":1182,"visitWebsite":1183,"imageProperties":1185,"theme":1187,"customClass":1188,"elementVersion":1190},"cimage-sH5K7FFn_vn",{"value":1181},{"hideDesktop":49,"hideMobile":49},{"value":154},{"value":1184},{"url":49,"newTab":55},{"value":1186},{"url":184,"compression":136},{"value":27},{"value":1189},[],{"value":537},"c-image",{"imageRadius":1193,"imageBorder":1195,"imageShadow":1197,"imageEffects":1199},{"value":1194},"img-none",{"value":1196},"img-border-none",{"value":1198},"img-shadow-none",{"value":1200},"img-effects-none",[1202,1239,1268,1302],{"extra":1203,"class":1217,"styles":1223,"wrapper":1233,"customCss":1234,"id":1235,"child":1236,"meta":1235,"title":1238,"tag":49},{"bgImage":1204,"overlayColor":1206,"left":1208,"popupDisabled":1210,"popupHide":1211,"minWidth":1212,"showPopupOnMouseOut":1214,"customClass":1215},{"value":1205},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":1207},"var(--color-lkd4di7k)",{"value":1209,"unit":96},50,{"value":55},{"value":55},{"value":1213},"full-page",{"value":27},{"value":1216},[],{"boxShadow":1218,"borders":1219,"borderRadius":1220,"radiusEdge":1222},{"value":27},{"value":306},{"value":1221},"radius5",{"value":27},{"paddingTop":1224,"paddingBottom":1225,"paddingLeft":1226,"paddingRight":1227,"marginTop":1228,"borderColor":1229,"borderWidth":1230,"borderStyle":1231,"backgroundColor":1232},{"unit":30,"value":33},{"unit":30,"value":33},{"unit":30,"value":33},{"value":33,"unit":30},{"unit":30,"value":31},{"value":40},{"value":59,"unit":30},{"value":46},{"value":213},{},[],"hl_main_popup",[1237],"row-_2Hy4yY6Hj","Popup",{"id":1237,"type":69,"child":1240,"class":1242,"styles":1248,"extra":1257,"wrapper":1265,"tagName":102,"meta":69,"title":103},[1241],"col-ONIwjdczOL",{"alignRow":1243,"boxShadow":1244,"borders":1245,"borderRadius":1246,"radiusEdge":1247},{"value":75},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":1249,"paddingRight":1250,"paddingTop":1251,"paddingBottom":1252,"backgroundColor":1253,"borderColor":1254,"borderWidth":1255,"borderStyle":1256},{"unit":30,"value":31},{"value":31,"unit":30},{"unit":30,"value":33},{"unit":30,"value":33},{"value":40},{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":1258,"bgImage":1260,"rowWidth":1262,"customClass":1263},{"value":1259},{"hideDesktop":55,"hideMobile":55},{"value":1261},{"url":49,"opacity":59,"options":60,"svgCode":49},{"value":372,"unit":96},{"value":1264},[],{"marginTop":1266,"marginBottom":1267},{"unit":30,"value":33},{"unit":30,"value":31},{"id":1241,"type":105,"child":1269,"class":1271,"styles":1276,"extra":1287,"wrapper":1299,"tagName":142,"meta":105,"title":143},[1270],"custom-code-CoC46R6UV7",{"boxShadow":1272,"borders":1273,"borderRadius":1274,"radiusEdge":1275},{"value":27},{"value":23},{"value":25},{"value":27},{"paddingLeft":1277,"paddingRight":1278,"paddingTop":1279,"paddingBottom":1280,"backgroundColor":1281,"width":1282,"borderColor":1284,"borderWidth":1285,"borderStyle":1286},{"unit":30,"value":33},{"value":33,"unit":30},{"unit":30,"value":31},{"unit":30,"value":31},{"value":40},{"value":1283,"unit":96},42.6,{"value":42},{"value":44,"unit":30},{"value":46},{"visibility":1288,"bgImage":1290,"columnLayout":1292,"justifyContentColumnLayout":1293,"alignContentColumnLayout":1294,"forceColumnLayoutForMobile":1295,"customClass":1296,"elementVersion":1298},{"value":1289},{"hideDesktop":55,"hideMobile":55},{"value":1291},{"url":49,"opacity":59,"options":60,"svgCode":49,"servingUrl":49,"placeholderBase64":49,"imageMeta":49,"videoUrl":49,"showSvgToggle":55},{"value":130},{"value":132},{"value":134},{"value":136},{"value":1297},[],{"value":537},{"marginTop":1300,"marginBottom":1301},{"unit":30,"value":33},{"unit":30,"value":31},{"extra":1303,"id":1270,"meta":423,"tagName":424,"class":1312},{"visibility":1304,"customCode":1306,"customClass":1309,"nodeId":1311},{"value":1305},{"hideMobile":49,"hideDesktop":49},{"value":1307},{"rawCustomCode":1308},"\u003C!-- Calendly inline widget begin -->\n\u003Cdiv class=\"calendly-inline-widget\" data-url=\"https://calendly.com/oli-3jx4/discovery-call\" style=\"min-width:320px;height:700px;\">\u003C/div>\n\u003Cscript type=\"text/javascript\" src=\"https://assets.calendly.com/assets/external/widget.js\" async>\u003C/script>\n\u003C!-- Calendly inline widget end -->",{"value":1310},[],"ccustom-code-CoC46R6UV7",{},"https://fonts.googleapis.com/css?family=Lato:100,200,300,400,500,600,700,800,900%7COpen%20Sans:100,200,300,400,500,600,700,800,900%7CMontserrat:100,200,300,400,500,600,700,800,900%7CRed%20Hat%20Display:100,200,300,400,500,600,700,800,900%7CRoboto:100,200,300,400,500,600,700,800,900%7C'Roboto':100,200,300,400,500,600,700,800,900%7CAkshar:100,200,300,400,500,600,700,800,900%7CPoppins:100,200,300,400,500,600,700,800,900%7CPlayfair%20Display:100,200,300,400,500,600,700,800,900%7C'Poppins':100,200,300,400,500,600,700,800,900%7C'Playfair%20Display':100,200,300,400,500,600,700,800,900&display=swap",{"author":1315,"canonicalMeta":1316,"customMeta":1317,"description":1318,"imageUrl":184,"keywords":49,"language":1319,"title":1320,"isPreviewUrl":55},"Oli Wood",[],[],"Instantly convert leads into customers with AI-powered text & voice follow-up. Book more calls. Never miss a lead again. See it live.","en","AI Lead Follow-Up Automation | Next Level Growth Partner","nextlevelgrowthpartner.com","/your-demo","tTmfQrkNxMWS3s6CulOU","Demo Page","ueNjo8LgFsCPSK7POBiq","https://stcdn.leadconnectorhq.com/funnel/icon/favicon.ico","31SGZ0ZGS4uwYBJplnRD","Automated Demo Funnel","4225eeaa-dcd6-4130-8c2c-814293fc93e3",[],["Reactive",1332],{"$snuxt-delay-hydration-mode":1333,"$snuxt-i18n-meta":1334,"$spreviewState":1335,"$smetaPixelOptions":1462},"manual",{},{"defaultSettings":1336,"mobileDevice":55,"funnelId":1327,"funnelDomain":1321,"stepId":1329,"locationId":1325,"funnelPageId":1323,"locationCode":1419,"funnelNextStep":1420,"fingerprint":49,"funnelNextPageId":1421,"stripePublishableKey":-1,"enablePaymentElement":-1,"merchantLoginId":49,"paypalPublishableKey":49,"merchantAccountId":-1,"stripeAccountId":-1,"isLivePaymentMode":136,"version":1422,"funnelSteps":1423,"cartItems":1436,"funnelName":1328,"orderFormVersion":537,"currency":1437,"blogSlug":49,"domain":1321,"pageUrl":1322,"pageName":1324,"affiliateId":-1,"videoExistsInPage":55,"pageType":1438,"contactId":-1,"email":-1,"phone":-1,"categoryId":49,"blogSearchTerm":49,"categoryUrlSlug":49,"authorSlug":49,"tagSlug":49,"authorId":49,"defaultPaymentProvider":49,"productCollections":1439,"ecomSelectedCollection":49,"imageOptimizationEnabled":136,"nmiMerchantGatewayId":49,"squareMerchantGatewayId":49,"fontsToLoad":1440,"ecomProductId":49,"isGdprCompliant":55,"isOptimisePageLoad":136,"ecommercePage":1428,"isBlogActive":55,"blogData":1452,"blogPaths":-1,"blogId":1327,"allowedCookies":1453,"paymentProviderDetails":-1,"events":1454,"searchTerm":49,"countryList":1455,"pixelToInit":1330,"formAction":49,"ecomProduct":1456,"requireCreditCard":136,"haveBlogWidget":55,"isFacebookIAB":55,"userAgent":49,"companyId":1457,"customerLoginToken":49,"cookieConsentList":1458,"cookieConsentExpiry":1459,"mediaFormats":1460},{"typography":1337,"background":1357,"percentWidth":1362,"offsetColor":1396,"progressBarSize":1409},{"fonts":1338,"colors":1349},{"headlineFont":1339,"contentFont":1345},{"id":1340,"text":1341,"value":1342},"headlinefont","Headline Font",{"text":1343,"value":1344},"Roboto","var(--roboto)",{"id":1346,"text":1347,"value":1348},"contentfont","Content Font",{"text":1343,"value":1344},{"textColor":1350,"linkColor":1353},{"value":1351},{"label":42,"value":1352},"#000000",{"value":1354},{"label":1355,"value":1356},"var(--blue)","#188bf6",{"bgImage":1358,"backgroundColor":1360},{"value":1359},{"url":49,"options":60},{"value":1361},"var(--color-m1tdwbyo)",[1363,1366,1369,1372,1375,1378,1381,1384,1387,1390,1393],{"text":1364,"value":1365},"0 Percent","progress0",{"text":1367,"value":1368},"10 Percent","progress10",{"text":1370,"value":1371},"20 Percent","progress20",{"text":1373,"value":1374},"30 Percent","progress30",{"text":1376,"value":1377},"40 Percent","progress40",{"text":1379,"value":1380},"50 Percent","progress50",{"text":1382,"value":1383},"60 Percent","progress60",{"text":1385,"value":1386},"70 Percent","progress70",{"text":1388,"value":1389},"80 Percent","progress80",{"text":1391,"value":1392},"90 Percent","progress90",{"text":1394,"value":1395},"100 Percent","progress100",[1397,1400,1403,1406],{"text":1398,"value":1399},"White","progressbarOffsetWhite",{"text":1401,"value":1402},"Transparent White","progressbarOffsetTransparentWhite",{"text":1404,"value":1405},"Black","progressbarOffsetBlack",{"text":1407,"value":1408},"Transparent Black","progressbarOffsetTransparentBlack",[1410,1413,1416],{"text":1411,"value":1412},"Small","progressbarSmall",{"text":1414,"value":1415},"Medium","progressbarMedium",{"text":1417,"value":1418},"Large","progressbarLarge","US","/revenue-tool","8CLuYzGGGS7BH7JsBjjX",4,[1424,1431,1432],{"url":1425,"value":1426,"type":1427,"sequence":1428,"id":1429,"name":1430},"/build-my-demo-641112-4501","5434af8a-6158-4401-b58b-fdc8e91f2afe","optin_funnel_page",1,"MNF5G4S6OYSaElj9m6WQ","Request A Demo",{"url":1322,"value":1329,"type":1427,"sequence":537,"id":1323,"name":1324},{"url":1420,"value":1433,"type":1427,"sequence":1434,"id":1421,"name":1435,"split":55},"e23145af-452a-498b-adce-deabebe3d37d",3,"Revenue Tool",[],"USD","funnel",[],[1441,1442,1443,1444,1445,1343,1446,1447,1448,1449,1450,1451],"Arial","Lato","Open Sans","Montserrat","Red Hat Display","'Roboto'","Akshar","Poppins","Playfair Display","'Poppins'","'Playfair Display'",{},[],[],[],{},"BMvoMAPkNGlwMrMqY1iO",[],["Date","2025-06-07T18:06:07.354Z"],{"formats":1461},[],{"pixelID":183,"disabled":55,"track":1463,"version":1464,"isEnabled":136,"pixelLoaded":55,"manualMode":136,"userData":183,"eventsQueue":1465},"PageView","2.0",[],["Set"],["ShallowReactive",1468],{"pageData":183}]</script>
<script>window.__NUXT__={};window.__NUXT__.config={public:{baseUrl:"https://apisystem.tech",newBaseURL:"https://backend.leadconnectorhq.com/appengine",serverBaseUrl:"https://apisystem.tech",NODE_ENV:"production",REVIEW_WIDGET_URL:"https://backend.leadconnectorhq.com/appengine/reviews/get_widget/",REST_API_URLS:"https://backend.leadconnectorhq.com",STATS_API_URL:"https://backend.leadconnectorhq.com",OLD_STORAGE_API_URL1_CDN:"https://cdn.msgsndr.com",OLD_STORAGE_API_URL2_CDN:"https://assets.cdn.msgsndr.com",STORAGE_API_URL1_CDN:"https://cdn.filesafe.space",STORAGE_API_URL2_CDN:"https://assets.cdn.filesafe.space",paymentsServiceUrl:"https://backend.leadconnectorhq.com",HLS_URL:"https://content.apisystem.tech",IMAGE_CDN:"https://images.leadconnectorhq.com",IMAGE_CDN_WHITELIST:["assets.cdn.msgsndr.com","cdn.msgsndr.com","cdn.filesafe.space","assets.cdn.filesafe.space","storage.googleapis.com","firebasestorage.googleapis.com"],authorizeAcceptJsUrlTestMode:"https://jstest.authorize.net/v1/Accept.js",authorizeAcceptJsUrlLiveMode:"https://js.authorize.net/v1/Accept.js",nmiPaymentProviderScriptUrl:"https://secure.safewebservices.com/token/Collect.js",FORMS_SERVICE_URL:"https://backend.leadconnectorhq.com",SURVEYS_SERVICE_URL:"https://backend.leadconnectorhq.com",GOOGLE_API_SERVICE_URL:"https://services.leadconnectorhq.com/common-google",ECOMMERCE_SERVICE_URL:"https://backend.leadconnectorhq.com/ecommerce",HL_HOMEPAGE_STEPID:"6dcfb06b-9734-44bd-bbcc-8bd4b7fec976",STRIPE_BNPL_CONFIGURATION_TEST:"pmc_1OaAR1FpU9DlKp7RH0HHU4xH",STRIPE_BNPL_CONFIGURATION_LIVE:"pmc_1OlnyOFpU9DlKp7R4tTHuihw",STRIPE_PMC_KEY_TEST:"pmc_1Ps2bTFpU9DlKp7RmgTzmJUL",STRIPE_PMC_KEY_LIVE:"pmc_1PzYYpFpU9DlKp7RcgxVmcvS",STRIPE_DEFAULT_CONFIGURATION_TEST:"pmc_1M95aRFpU9DlKp7ReIqqY0PP",STRIPE_DEFAULT_CONFIGURATION_LIVE:"pmc_1NYilsFpU9DlKp7RkMiUNrKE",STRIPE_DEFAULT_CONFIGURATION_TEST_SURVEY:"pmc_1QrvB7FpU9DlKp7RcL9L2idV",STRIPE_DEFAULT_CONFIGURATION_LIVE_SURVEY:"pmc_1Qwds7FpU9DlKp7RMCBlclQ0",STRIPE_DEFAULT_CONFIGURATION_TEST_FORM:"pmc_1Qodu1FpU9DlKp7RoWHB8Txx",STRIPE_DEFAULT_CONFIGURATION_LIVE_FORM:"pmc_1QwcV0FpU9DlKp7RORUHPK8B",ENTERPRISE_RECAPTCHA_SITE_KEY:"6LeDBFwpAAAAAJe8ux9-imrqZ2ueRsEtdiWoDDpX",STRIPE_ONE_STEP_PMC_ID_TEST:"pmc_1QodqYFpU9DlKp7R8EIapiwE",STRIPE_TWO_STEP_PMC_ID_TEST:"pmc_1QodsEFpU9DlKp7RHRyay2KC",STRIPE_ONE_STEP_PMC_ID_LIVE:"pmc_1QwcLrFpU9DlKp7Rl9zb07x1",STRIPE_TWO_STEP_PMC_ID_LIVE:"pmc_1QwcP4FpU9DlKp7R4L0ytWkJ",RECAPTCHA_SITE_KEY:"6LfcbMseAAAAAI-EJoB-lUh7_TJaYloLbcbmnhEO",i18n:{baseUrl:"",defaultLocale:"en",defaultDirection:"ltr",strategy:"no_prefix",lazy:true,rootRedirect:"",routesNameSeparator:"___",defaultLocaleRouteNameSuffix:"default",skipSettingLocaleOnNavigate:false,differentDomains:false,trailingSlash:false,locales:[{code:"da",files:["/tmp/workspace/production/common/builder-preview/locales/da.json"]},{code:"de",files:["/tmp/workspace/production/common/builder-preview/locales/de.json"]},{code:"en",files:["/tmp/workspace/production/common/builder-preview/locales/en_US.json"]},{code:"es",files:["/tmp/workspace/production/common/builder-preview/locales/es.json"]},{code:"fi",files:["/tmp/workspace/production/common/builder-preview/locales/fi.json"]},{code:"fr-ca",files:["/tmp/workspace/production/common/builder-preview/locales/fr_CA.json"]},{code:"fr",files:["/tmp/workspace/production/common/builder-preview/locales/fr.json"]},{code:"hu",files:["/tmp/workspace/production/common/builder-preview/locales/hu.json"]},{code:"it",files:["/tmp/workspace/production/common/builder-preview/locales/it.json"]},{code:"nl",files:["/tmp/workspace/production/common/builder-preview/locales/nl.json"]},{code:"no",files:["/tmp/workspace/production/common/builder-preview/locales/no.json"]},{code:"pl",files:["/tmp/workspace/production/common/builder-preview/locales/pl.json"]},{code:"pt-br",files:["/tmp/workspace/production/common/builder-preview/locales/pt_BR.json"]},{code:"pt",files:["/tmp/workspace/production/common/builder-preview/locales/pt.json"]},{code:"sv",files:["/tmp/workspace/production/common/builder-preview/locales/sv.json"]}],detectBrowserLanguage:{alwaysRedirect:false,cookieCrossOrigin:false,cookieDomain:"",cookieKey:"i18n_redirected",cookieSecure:false,fallbackLocale:"",redirectOn:"root",useCookie:false},experimental:{localeDetector:"",switchLocalePathLinkSSR:false,autoImportTranslationFunctions:false,typedPages:true,typedOptionsAndMessages:false,generatedLocaleFilePathFormat:"absolute"},multiDomainLocales:false}},app:{baseURL:"/",buildId:"c339d604-0540-461d-b87e-aa8d8db4991a",buildAssetsDir:"/_preview/",cdnURL:"https://stcdn.leadconnectorhq.com/"}}</script><script> (function() { const w = window; w._$delayHydration = (function() { if (!('requestIdleCallback' in w) || !('requestAnimationFrame' in w)) { return new Promise(resolve => resolve('not supported')) } function eventListeners(){const c=new AbortController;const p=new Promise(resolve=>{const hydrateOnEvents="mousemove,scroll,keydown,click,touchstart,wheel".split(",");function handler(e){hydrateOnEvents.forEach(e=>w.removeEventListener(e,handler));requestAnimationFrame(()=>resolve(e))}hydrateOnEvents.forEach(e=>{w.addEventListener(e,handler,{capture:true,once:true,passive:true,signal:c.signal})})});return{c:()=>c.abort(),p:p}}function idleListener(){let id;const p=new Promise(resolve=>{const isMobile=w.innerWidth<640;const timeout=isMobile?Number.parseInt("5000"):Number.parseInt("4500");const timeoutDelay=()=>setTimeout(()=>requestAnimationFrame(()=>resolve("timeout")),timeout);id=w.requestIdleCallback(timeoutDelay,{timeout:Number.parseInt("2000")})});return{c:()=>window.cancelIdleCallback(id),p:p}}const triggers=[idleListener(),eventListeners()];const hydrationPromise=Promise.race(triggers.map(t=>t.p)).finally(()=>{triggers.forEach(t=>t.c())}); return hydrationPromise; })(); ;(()=>{w._$delayHydration.then(e=>{if(e instanceof PointerEvent||e instanceof MouseEvent&&e.type==="click"||window.TouchEvent&&e instanceof TouchEvent){setTimeout(()=>w.requestIdleCallback(()=>setTimeout(()=>e.target&&e.target.click(),500)),50)}})})(); })(); </script><script defer src="https://static.cloudflareinsights.com/beacon.min.js/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon='{"rayId":"94c772f9386e8af7","serverTiming":{"name":{"cfExtPri":true,"cfEdge":true,"cfOrigin":true,"cfL4":true,"cfSpeedBrain":true,"cfCacheStatus":true}},"version":"2025.5.0","token":"d56c0fb9091e40fc8f3829f8a67134fa"}' crossorigin="anonymous"></script>
</body></html>